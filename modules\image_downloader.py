#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片下载模块
使用Selenium控制Chrome浏览器爬取谷歌图片
"""

import os
import time
import random
import logging
import urllib.request
import urllib.parse
import urllib.error
from pathlib import Path
from typing import List, Optional, Dict, Any, Callable

import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# 导入WebDriver管理器
from modules.webdriver_manager import ChromeDriverManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ImageDownloader")

class ImageDownloader:
    """图片下载器，使用Selenium控制Chrome浏览器爬取谷歌图片"""

    def __init__(self, config_manager=None):
        """
        初始化图片下载器

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.driver = None
        self.chrome_driver_path = self._get_chrome_driver_path()

        # 预定义的备用图片URL列表（风景、自然等通用图片）
        self.fallback_images = [
            "https://images.pexels.com/photos/1619317/pexels-photo-1619317.jpeg",  # 山脉
            "https://images.pexels.com/photos/1287145/pexels-photo-1287145.jpeg",  # 海滩
            "https://images.pexels.com/photos/1166209/pexels-photo-1166209.jpeg",  # 森林
            "https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg",  # 城市
            "https://images.pexels.com/photos/1563356/pexels-photo-1563356.jpeg",  # 日落
            "https://images.pexels.com/photos/1323550/pexels-photo-1323550.jpeg",  # 湖泊
            "https://images.pexels.com/photos/1486974/pexels-photo-1486974.jpeg",  # 花朵
            "https://images.pexels.com/photos/1420440/pexels-photo-1420440.jpeg",  # 天空
        ]

    def _get_chrome_driver_path(self) -> str:
        """
        获取Chrome驱动路径，如果没有找到则自动下载

        Returns:
            str: Chrome驱动路径
        """
        # 首先检查配置中是否有设置
        if self.config_manager:
            driver_path = self.config_manager.get("general_settings.image.chrome_driver_path", "")
            if driver_path and os.path.exists(driver_path):
                logger.info(f"使用配置中的Chrome驱动: {driver_path}")
                return driver_path

        # 检查当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        driver_path = os.path.join(current_dir, "chromedriver.exe")
        if os.path.exists(driver_path):
            logger.info(f"使用当前目录中的Chrome驱动: {driver_path}")
            return driver_path

        # 检查上级目录
        parent_dir = os.path.dirname(current_dir)
        driver_path = os.path.join(parent_dir, "chromedriver.exe")
        if os.path.exists(driver_path):
            logger.info(f"使用上级目录中的Chrome驱动: {driver_path}")
            return driver_path

        # 如果没有找到驱动，尝试自动下载
        logger.info("未找到Chrome驱动，尝试自动下载...")
        try:
            # 创建ChromeDriverManager实例
            driver_manager = ChromeDriverManager(self.config_manager)

            # 安装驱动
            driver_path = driver_manager.install_driver()

            if driver_path and os.path.exists(driver_path):
                logger.info(f"自动下载Chrome驱动成功: {driver_path}")

                # 更新配置
                if self.config_manager:
                    driver_manager.update_config(driver_path)

                return driver_path
        except Exception as e:
            logger.error(f"自动下载Chrome驱动失败: {e}")

        # 如果自动下载失败，返回默认路径，让Selenium自己去找
        logger.warning("未找到Chrome驱动，将使用默认路径")
        return "chromedriver.exe"

    def _initialize_driver(self) -> bool:
        """
        初始化Chrome驱动

        Returns:
            bool: 是否成功初始化
        """
        try:
            # 设置Chrome选项
            chrome_options = Options()

            # 基本设置
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")

            # 反检测设置
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option("useAutomationExtension", False)

            # 设置更真实的用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.49 Safari/537.36")

            # 其他设置
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-infobars")
            chrome_options.add_argument("--mute-audio")
            chrome_options.add_argument("--disable-notifications")
            chrome_options.add_argument("--disable-popup-blocking")

            # 使用无头模式，但添加窗口大小以模拟真实浏览器
            chrome_options.add_argument("--headless=new")  # 新版无头模式
            chrome_options.add_argument("--window-size=1920,1080")

            # 获取代理设置
            if self.config_manager:
                proxy = self.config_manager.get("general_settings.image.proxy", "")
                if proxy:
                    logger.info(f"使用代理: {proxy}")
                    chrome_options.add_argument(f"--proxy-server={proxy}")

            # 初始化Chrome驱动
            logger.info(f"使用Chrome驱动路径: {self.chrome_driver_path}")

            # 检查驱动文件是否存在
            if not os.path.exists(self.chrome_driver_path):
                logger.error(f"Chrome驱动文件不存在: {self.chrome_driver_path}")

                # 尝试自动下载
                logger.info("尝试自动下载Chrome驱动...")
                driver_manager = ChromeDriverManager(self.config_manager)
                driver_path = driver_manager.install_driver()

                if driver_path and os.path.exists(driver_path):
                    logger.info(f"自动下载Chrome驱动成功: {driver_path}")
                    self.chrome_driver_path = driver_path

                    # 更新配置
                    if self.config_manager:
                        driver_manager.update_config(driver_path)
                else:
                    logger.error("自动下载Chrome驱动失败")
                    return False

            service = Service(executable_path=self.chrome_driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(30)  # 设置页面加载超时时间

            logger.info("Chrome驱动初始化成功")
            return True
        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {e}")

            # 如果是版本不匹配的错误，尝试自动下载匹配的驱动
            error_str = str(e).lower()
            if "version" in error_str and "chrome" in error_str:
                logger.info("检测到Chrome版本不匹配，尝试自动下载匹配的驱动...")
                try:
                    driver_manager = ChromeDriverManager(self.config_manager)
                    driver_path = driver_manager.install_driver()

                    if driver_path and os.path.exists(driver_path):
                        logger.info(f"自动下载匹配的Chrome驱动成功: {driver_path}")
                        self.chrome_driver_path = driver_path

                        # 更新配置
                        if self.config_manager:
                            driver_manager.update_config(driver_path)

                        # 重新尝试初始化驱动
                        return self._initialize_driver()
                except Exception as e2:
                    logger.error(f"自动下载匹配的Chrome驱动失败: {e2}")

            self.driver = None
            return False

    def _close_driver(self):
        """关闭Chrome驱动"""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                logger.error(f"关闭Chrome驱动失败: {e}")
            finally:
                self.driver = None

    def search_and_download(self, keyword: str, output_dir: str, limit: int = 5,
                           progress_callback: Callable = None) -> List[str]:
        """
        搜索并下载图片

        Args:
            keyword: 搜索关键词
            output_dir: 输出目录
            limit: 下载图片数量限制
            progress_callback: 进度回调函数

        Returns:
            List[str]: 下载的图片路径列表
        """
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 初始化结果列表
        downloaded_images = []

        # 尝试使用Selenium下载
        if self._initialize_driver():
            try:
                # 构建搜索URL - 使用Google图片搜索，并添加大图片过滤参数
                # 获取配置中的图片尺寸要求
                size_filter = ""
                if self.config_manager:
                    size_setting = self.config_manager.get("general_settings.image.size", ">1024*768")
                    if size_setting == ">1024*768":
                        size_filter = "&tbs=isz:l"  # 大图片
                    elif size_setting == ">2048*1536":
                        size_filter = "&tbs=isz:lt,islt:2mp"  # 超过2MP的图片
                    elif size_setting == ">4096*3072":
                        size_filter = "&tbs=isz:lt,islt:12mp"  # 超过12MP的图片

                # 使用Google图片搜索，添加高质量图片过滤
                search_url = f"https://www.google.com/search?q={urllib.parse.quote(keyword)}&tbm=isch{size_filter}&safe=off&source=lnt"

                # 打开搜索页面
                if progress_callback:
                    progress_callback(0, limit, f"正在打开搜索页面: {keyword}")

                # 在打开页面前，添加一个执行脚本来绕过自动化检测
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                # 打开搜索页面
                self.driver.get(search_url)

                # 随机等待一段时间，模拟人类行为
                time.sleep(random.uniform(1.5, 3.0))

                # 等待页面加载 - 更新为Google图片搜索的选择器
                try:
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "img[data-src], img[src]"))
                    )
                except Exception as e:
                    logger.warning(f"等待页面加载超时: {e}")

                # 滚动页面加载更多图片
                for i in range(3):
                    if progress_callback:
                        progress_callback(0, limit, f"正在加载更多图片 ({i+1}/3): {keyword}")

                    # 随机滚动，模拟人类行为
                    scroll_height = random.randint(300, 800)
                    self.driver.execute_script(f"window.scrollBy(0, {scroll_height});")

                    # 随机等待
                    time.sleep(random.uniform(0.8, 2.0))

                # 最后滚动到底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(1.5)

                # 提取图片链接 - 针对Google图片搜索优化
                try:
                    img_urls = []

                    # 方法1: 点击图片获取高分辨率原图
                    img_urls.extend(self._get_high_resolution_images(limit))

                    # 方法2: 如果方法1失败，使用传统方式提取图片链接
                    if not img_urls:
                        # Google图片搜索的选择器
                        selectors = [
                            "img[data-src]",  # Google图片的主要选择器
                            "img[src*='gstatic']",  # Google静态图片
                            "img[src*='googleusercontent']",  # Google用户内容
                            ".rg_i",  # Google图片结果
                            "img.rg_i",  # 另一种Google图片选择器
                        ]

                        for selector in selectors:
                            img_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            if img_elements:
                                logger.info(f"找到 {len(img_elements)} 个图片元素，使用选择器: {selector}")

                                # 优先获取data-src属性（通常是高分辨率图片）
                                for img in img_elements:
                                    data_src = img.get_attribute("data-src")
                                    src = img.get_attribute("src")

                                    if data_src and data_src.startswith("http"):
                                        img_urls.append(data_src)
                                    elif src and src.startswith("http"):
                                        img_urls.append(src)

                                if img_urls:
                                    break

                        # 如果仍然没有找到图片，尝试获取所有img标签
                        if not img_urls:
                            img_elements = self.driver.find_elements(By.TAG_NAME, "img")
                            for img in img_elements:
                                src = img.get_attribute("src")
                                if src and src.startswith("http") and "google" not in src.lower():
                                    img_urls.append(src)

                    # 保存页面源代码以便调试
                    if not img_urls:
                        page_source = self.driver.page_source
                        debug_file = os.path.join(output_dir, "debug_page_source.html")
                        with open(debug_file, "w", encoding="utf-8") as f:
                            f.write(page_source)
                        logger.warning(f"未找到图片，已保存页面源代码到: {debug_file}")

                    # 过滤掉base64编码的图片和无效URL
                    img_urls = [url for url in img_urls if url and not url.startswith("data:") and url.startswith("http")]

                    # 去重
                    img_urls = list(dict.fromkeys(img_urls))

                    logger.info(f"找到 {len(img_urls)} 个有效图片URL")
                except Exception as e:
                    logger.error(f"提取图片链接失败: {e}")
                    img_urls = []

                # 下载图片
                for i, url in enumerate(img_urls[:limit]):
                    if progress_callback:
                        progress_callback(i, limit, f"正在下载图片 {i+1}/{limit}")

                    try:
                        # 生成输出文件名
                        # 尝试从URL中获取文件扩展名
                        file_ext = os.path.splitext(url.split('?')[0].split('/')[-1])[1]
                        if not file_ext or len(file_ext) > 5:  # 如果扩展名不存在或异常长
                            file_ext = ".jpg"  # 默认扩展名

                        output_file = os.path.join(output_dir, f"image_{int(time.time())}_{random.randint(1000, 9999)}{file_ext}")

                        # 设置请求头，模拟浏览器
                        headers = {
                            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.49 Safari/537.36",
                            "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
                            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                            "Referer": search_url,  # 设置Referer为搜索URL
                            "Sec-Fetch-Dest": "image",
                            "Sec-Fetch-Mode": "no-cors",
                            "Sec-Fetch-Site": "cross-site"
                        }

                        # 下载图片，使用更长的超时时间
                        response = requests.get(url, headers=headers, timeout=15, stream=True)
                        response.raise_for_status()  # 如果状态码不是200，抛出异常

                        # 检查内容类型是否为图片
                        content_type = response.headers.get('Content-Type', '')
                        if not content_type.startswith('image/'):
                            logger.warning(f"跳过非图片内容: {url}, Content-Type: {content_type}")
                            continue

                        # 检查图片大小
                        content_length = int(response.headers.get('Content-Length', 0))
                        if content_length < 1000:  # 小于1KB的可能是无效图片
                            logger.warning(f"跳过小图片: {url}, 大小: {content_length} 字节")
                            continue

                        # 保存图片
                        with open(output_file, "wb") as f:
                            for chunk in response.iter_content(chunk_size=8192):
                                if chunk:
                                    f.write(chunk)

                        # 验证下载的文件是否为有效图片
                        try:
                            from PIL import Image
                            img = Image.open(output_file)
                            img.verify()  # 验证图片完整性

                            # 获取图片尺寸
                            img = Image.open(output_file)
                            width, height = img.size

                            # 根据配置检查图片尺寸
                            min_width, min_height = self._get_minimum_size()
                            if width < min_width or height < min_height:
                                logger.warning(f"跳过尺寸不符合要求的图片: {output_file}, 尺寸: {width}x{height}, 要求: {min_width}x{min_height}")
                                os.remove(output_file)
                                continue

                            logger.info(f"成功下载图片: {output_file}, 尺寸: {width}x{height}")
                            downloaded_images.append(output_file)
                        except Exception as e:
                            logger.warning(f"下载的文件不是有效图片: {output_file}, 错误: {e}")
                            os.remove(output_file)
                            continue

                        # 如果已经下载了足够的图片，就停止
                        if len(downloaded_images) >= limit:
                            break

                        # 随机等待一段时间，避免被封
                        time.sleep(random.uniform(0.5, 1.5))
                    except Exception as e:
                        logger.error(f"下载图片失败: {url}, 错误: {e}")
                        continue
            except Exception as e:
                logger.error(f"使用Selenium搜索图片失败: {e}")
            finally:
                # 关闭驱动
                self._close_driver()

        # 如果没有下载到图片，使用备用方法
        if not downloaded_images:
            logger.info("使用备用方法下载图片")
            downloaded_images = self._download_fallback_images(output_dir, limit)

        return downloaded_images

    def _get_high_resolution_images(self, limit: int) -> List[str]:
        """
        通过点击图片获取高分辨率原图URL

        Args:
            limit: 需要获取的图片数量

        Returns:
            List[str]: 高分辨率图片URL列表
        """
        high_res_urls = []

        try:
            # 查找可点击的图片元素
            clickable_images = self.driver.find_elements(By.CSS_SELECTOR, "img.rg_i, .rg_i img, [data-ved] img")

            if not clickable_images:
                # 尝试其他选择器
                clickable_images = self.driver.find_elements(By.CSS_SELECTOR, "img[data-src], img[src]")

            logger.info(f"找到 {len(clickable_images)} 个可点击的图片")

            # 点击前几张图片获取高分辨率版本
            for i, img_element in enumerate(clickable_images[:limit * 2]):  # 多点击一些以防失败
                try:
                    # 滚动到图片位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", img_element)
                    time.sleep(0.5)

                    # 点击图片
                    img_element.click()
                    time.sleep(random.uniform(1.0, 2.0))

                    # 查找高分辨率图片
                    # Google图片点击后会显示大图
                    high_res_selectors = [
                        "img.n3VNCb",  # Google大图选择器
                        "img.iPVvYb",  # 另一种大图选择器
                        ".v4dQwb img",  # 图片预览区域
                        ".tvh9oe img",  # 另一种预览区域
                        "img[style*='max-width']",  # 有最大宽度样式的图片
                    ]

                    for selector in high_res_selectors:
                        high_res_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in high_res_elements:
                            src = element.get_attribute("src")
                            data_src = element.get_attribute("data-src")

                            # 优先使用data-src，然后是src
                            url = data_src if data_src and data_src.startswith("http") else src

                            if url and url.startswith("http") and url not in high_res_urls:
                                # 检查URL是否可能是高分辨率图片
                                if self._is_likely_high_resolution_url(url):
                                    high_res_urls.append(url)
                                    logger.info(f"获取到高分辨率图片URL: {url[:100]}...")

                                    if len(high_res_urls) >= limit:
                                        return high_res_urls

                    # 如果已经获取足够的图片，退出循环
                    if len(high_res_urls) >= limit:
                        break

                except Exception as e:
                    logger.warning(f"点击第{i+1}张图片失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"获取高分辨率图片失败: {e}")

        return high_res_urls

    def _is_likely_high_resolution_url(self, url: str) -> bool:
        """
        判断URL是否可能是高分辨率图片

        Args:
            url: 图片URL

        Returns:
            bool: 是否可能是高分辨率图片
        """
        # 排除明显的缩略图或小图片
        exclude_patterns = [
            "thumbnail", "thumb", "small", "icon", "avatar",
            "=s64", "=s128", "=s256",  # Google图片尺寸参数
            "w=150", "w=200", "w=300",  # 宽度参数
            "h=150", "h=200", "h=300",  # 高度参数
        ]

        url_lower = url.lower()
        for pattern in exclude_patterns:
            if pattern in url_lower:
                return False

        # 包含高分辨率指示的URL
        include_patterns = [
            "=w1920", "=w1600", "=w1200", "=w1024",  # 大宽度
            "=h1080", "=h900", "=h768",  # 大高度
            "large", "original", "full", "hd",
        ]

        for pattern in include_patterns:
            if pattern in url_lower:
                return True

        return True  # 默认认为是高分辨率

    def _get_minimum_size(self) -> tuple:
        """
        根据配置获取最小图片尺寸要求

        Returns:
            tuple: (最小宽度, 最小高度)
        """
        if self.config_manager:
            size_setting = self.config_manager.get("general_settings.image.size", ">1024*768")

            if size_setting == ">1024*768":
                return (1024, 768)
            elif size_setting == ">2048*1536":
                return (2048, 1536)
            elif size_setting == ">4096*3072":
                return (4096, 3072)
            elif size_setting == ">800*600":
                return (800, 600)
            elif size_setting == ">1920*1080":
                return (1920, 1080)

        # 默认要求
        return (1024, 768)

    def _download_fallback_images(self, output_dir: str, limit: int = 1) -> List[str]:
        """
        下载备用图片

        Args:
            output_dir: 输出目录
            limit: 下载图片数量限制

        Returns:
            List[str]: 下载的图片路径列表
        """
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 初始化结果列表
        downloaded_images = []

        # 使用更可靠的备用图片源 - Unsplash API
        try:
            # 使用Unsplash API获取随机图片
            logger.info("尝试从Unsplash获取随机图片")
            unsplash_urls = self._get_unsplash_images(limit)

            if unsplash_urls:
                logger.info(f"从Unsplash获取到 {len(unsplash_urls)} 张图片")
                selected_urls = unsplash_urls
            else:
                # 如果Unsplash API失败，使用本地备用图片
                logger.info("使用本地备用图片")
                selected_urls = random.sample(self.fallback_images, min(limit, len(self.fallback_images)))
        except Exception as e:
            logger.error(f"获取Unsplash图片失败: {e}")
            # 使用本地备用图片
            selected_urls = random.sample(self.fallback_images, min(limit, len(self.fallback_images)))

        # 下载图片
        for i, url in enumerate(selected_urls):
            try:
                # 生成输出文件名
                file_ext = os.path.splitext(url.split('?')[0].split('/')[-1])[1]
                if not file_ext or len(file_ext) > 5:
                    file_ext = ".jpg"

                output_file = os.path.join(output_dir, f"image_{int(time.time())}_{random.randint(1000, 9999)}{file_ext}")

                # 设置请求头，模拟浏览器
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.49 Safari/537.36",
                    "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
                    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                    "Referer": "https://www.google.com/",
                    "Sec-Fetch-Dest": "image",
                    "Sec-Fetch-Mode": "no-cors",
                    "Sec-Fetch-Site": "cross-site"
                }

                # 下载图片
                response = requests.get(url, headers=headers, timeout=15, stream=True)
                response.raise_for_status()

                # 保存图片
                with open(output_file, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                # 验证下载的文件是否为有效图片
                try:
                    from PIL import Image
                    img = Image.open(output_file)
                    img.verify()  # 验证图片完整性

                    # 获取图片尺寸
                    img = Image.open(output_file)
                    width, height = img.size

                    logger.info(f"成功下载备用图片: {output_file}, 尺寸: {width}x{height}")
                    downloaded_images.append(output_file)
                except Exception as e:
                    logger.warning(f"下载的文件不是有效图片: {output_file}, 错误: {e}")
                    os.remove(output_file)
                    continue

                # 随机等待一段时间，避免被封
                time.sleep(random.uniform(0.3, 0.8))
            except Exception as e:
                logger.error(f"下载备用图片失败: {url}, 错误: {e}")
                continue

        return downloaded_images

    def _get_unsplash_images(self, limit: int = 1) -> List[str]:
        """
        从Unsplash获取随机图片URL

        Args:
            limit: 获取图片数量

        Returns:
            List[str]: 图片URL列表
        """
        try:
            # 使用Unsplash随机图片API
            urls = []

            # 使用不同的关键词获取多样化的图片
            keywords = ["nature", "landscape", "city", "architecture", "technology", "abstract", "animal", "food", "travel"]
            selected_keywords = random.sample(keywords, min(limit, len(keywords)))

            # 根据配置获取合适的图片尺寸
            min_width, min_height = self._get_minimum_size()
            # 确保获取的图片尺寸满足要求，稍微增加一些以确保质量
            width = max(min_width, 1920)
            height = max(min_height, 1080)

            for keyword in selected_keywords:
                # 构建URL，使用更大的尺寸
                url = f"https://source.unsplash.com/random/{width}x{height}?{keyword}"

                # 添加随机参数避免缓存
                url = f"{url}&random={random.randint(1, 10000)}"

                urls.append(url)

                # 如果已经获取了足够的URL，就停止
                if len(urls) >= limit:
                    break

            # 如果还需要更多URL，使用随机图片API
            while len(urls) < limit:
                url = f"https://source.unsplash.com/random/{width}x{height}?random={random.randint(1, 10000)}"
                urls.append(url)

            return urls
        except Exception as e:
            logger.error(f"获取Unsplash图片URL失败: {e}")
            return []

    def batch_download_for_shots(self, shots: List[Dict[str, Any]],
                               output_dir: str, progress_callback: Callable = None) -> List[Dict[str, Any]]:
        """
        为分镜批量下载图片

        Args:
            shots: 分镜列表
            output_dir: 输出目录
            progress_callback: 进度回调函数

        Returns:
            List[Dict[str, Any]]: 更新后的分镜列表
        """
        # 获取图片下载设置
        if self.config_manager:
            image_settings = self.config_manager.get("general_settings.image", {})
            image_limit = image_settings.get("limit", 1)
        else:
            image_limit = 1

        # 创建一个副本，避免修改原始数据
        result_shots = shots.copy()
        total = len(shots)

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        for i, shot in enumerate(result_shots):
            try:
                # 准备搜索关键词
                en_prompt = shot.get("en_prompt", "")
                en_tags = shot.get("en_tags", "")
                prompt = shot.get("prompt", "")  # 中文提示词

                # 初始化搜索关键词
                search_query = en_prompt
                if en_tags:
                    # 将标签添加到搜索关键词
                    search_query = f"{search_query} {en_tags}"

                # 如果没有英文关键词，使用中文
                if not search_query and prompt:
                    search_query = prompt

                # 如果仍然没有关键词，使用默认关键词
                if not search_query:
                    search_query = "nature landscape"

                # 更新进度
                if progress_callback:
                    progress_callback(i, total, f"搜索图片中: {shot.get('id', i+1)}")

                # 创建分镜专用目录
                shot_dir = os.path.join(output_dir, f"shot_{shot.get('id', i+1)}")

                # 搜索并下载图片
                image_paths = self.search_and_download(
                    search_query,
                    shot_dir,
                    limit=image_limit,
                    progress_callback=lambda _current, _total, status: progress_callback(
                        i, total, f"分镜 {shot.get('id', i+1)}: {status}"
                    ) if progress_callback else None
                )

                # 如果找到图片，保存第一张图片路径
                if image_paths:
                    shot["image_path"] = image_paths[0]

                    # 如果下载了多张图片，保存所有图片路径
                    if len(image_paths) > 1:
                        shot["image_candidates"] = image_paths

                    # 更新进度
                    if progress_callback:
                        progress_callback(i+1, total, f"图片下载完成: {shot.get('id', i+1)}")
                else:
                    # 更新进度
                    if progress_callback:
                        progress_callback(i+1, total, f"未找到图片: {shot.get('id', i+1)}")
            except Exception as e:
                logger.error(f"为分镜下载图片失败: {e}")
                # 更新进度
                if progress_callback:
                    progress_callback(i+1, total, f"图片下载失败: {shot.get('id', i+1)} - {e}")

        return result_shots
