#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停止按钮功能测试脚本
验证停止按钮是否正常工作
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer

from config import ConfigManager
from ui.main_window import MainWindow

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("StopButtonTest")

class StopButtonTester:
    """停止按钮测试器"""
    
    def __init__(self):
        self.app = QApplication.instance() or QApplication(sys.argv)
        self.config_manager = ConfigManager()
        self.main_window = None
        
    def test_stop_button_visibility(self):
        """测试停止按钮是否可见"""
        print("🔍 测试停止按钮可见性...")
        
        # 创建主窗口
        self.main_window = MainWindow(self.config_manager)
        
        # 检查停止按钮是否存在
        if hasattr(self.main_window.title_bar, 'stop_button'):
            print("  ✅ 停止按钮已创建")
            
            # 检查按钮状态
            if self.main_window.title_bar.stop_button.isEnabled():
                print("  ⚠️  停止按钮默认应该是禁用状态")
                return False
            else:
                print("  ✅ 停止按钮默认禁用状态正确")
                
            # 检查按钮文本
            button_text = self.main_window.title_bar.stop_button.text()
            if button_text == "停止":
                print("  ✅ 停止按钮文本正确")
            else:
                print(f"  ❌ 停止按钮文本错误: {button_text}")
                return False
                
            return True
        else:
            print("  ❌ 停止按钮未创建")
            return False
    
    def test_stop_button_enable_disable(self):
        """测试停止按钮启用/禁用功能"""
        print("\n🔧 测试停止按钮启用/禁用功能...")
        
        if not self.main_window:
            print("  ❌ 主窗口未初始化")
            return False
        
        # 测试启用停止按钮
        self.main_window.title_bar.set_stop_button_enabled(True)
        if self.main_window.title_bar.stop_button.isEnabled():
            print("  ✅ 停止按钮启用功能正常")
        else:
            print("  ❌ 停止按钮启用功能失败")
            return False
        
        # 测试禁用停止按钮
        self.main_window.title_bar.set_stop_button_enabled(False)
        if not self.main_window.title_bar.stop_button.isEnabled():
            print("  ✅ 停止按钮禁用功能正常")
        else:
            print("  ❌ 停止按钮禁用功能失败")
            return False
        
        return True
    
    def test_task_management(self):
        """测试任务管理功能"""
        print("\n⚙️ 测试任务管理功能...")
        
        if not self.main_window:
            print("  ❌ 主窗口未初始化")
            return False
        
        # 测试设置任务运行状态
        self.main_window._set_task_running("测试任务")
        
        # 检查任务状态
        if self.main_window.current_task == "测试任务":
            print("  ✅ 任务状态设置正确")
        else:
            print(f"  ❌ 任务状态设置错误: {self.main_window.current_task}")
            return False
        
        # 检查停止按钮是否启用
        if self.main_window.title_bar.stop_button.isEnabled():
            print("  ✅ 任务运行时停止按钮已启用")
        else:
            print("  ❌ 任务运行时停止按钮未启用")
            return False
        
        # 检查其他按钮是否禁用
        if not self.main_window.btn_translate.isEnabled():
            print("  ✅ 任务运行时其他按钮已禁用")
        else:
            print("  ❌ 任务运行时其他按钮未禁用")
            return False
        
        # 测试设置任务完成状态
        self.main_window._set_task_finished()
        
        # 检查任务状态
        if self.main_window.current_task is None:
            print("  ✅ 任务完成状态设置正确")
        else:
            print(f"  ❌ 任务完成状态设置错误: {self.main_window.current_task}")
            return False
        
        # 检查停止按钮是否禁用
        if not self.main_window.title_bar.stop_button.isEnabled():
            print("  ✅ 任务完成时停止按钮已禁用")
        else:
            print("  ❌ 任务完成时停止按钮未禁用")
            return False
        
        return True
    
    def test_stop_signal(self):
        """测试停止信号连接"""
        print("\n📡 测试停止信号连接...")
        
        if not self.main_window:
            print("  ❌ 主窗口未初始化")
            return False
        
        # 检查信号是否连接
        signal_connected = False
        try:
            # 检查信号连接（这是一个简化的检查）
            if hasattr(self.main_window, '_on_stop_task'):
                print("  ✅ 停止任务方法存在")
                signal_connected = True
            else:
                print("  ❌ 停止任务方法不存在")
                return False
        except Exception as e:
            print(f"  ❌ 检查信号连接时出错: {e}")
            return False
        
        return signal_connected
    
    def test_ui_layout(self):
        """测试UI布局"""
        print("\n🎨 测试UI布局...")
        
        if not self.main_window:
            print("  ❌ 主窗口未初始化")
            return False
        
        # 显示主窗口
        self.main_window.show()
        
        # 检查停止按钮在标题栏中的位置
        title_bar = self.main_window.title_bar
        layout = title_bar.layout()
        
        # 查找停止按钮在布局中的位置
        stop_button_found = False
        settings_button_found = False
        stop_after_settings = False
        
        for i in range(layout.count()):
            item = layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if widget == title_bar.settings_button:
                    settings_button_found = True
                elif widget == title_bar.stop_button and settings_button_found:
                    stop_after_settings = True
                    stop_button_found = True
                    break
        
        if stop_button_found and stop_after_settings:
            print("  ✅ 停止按钮位置正确（在设置按钮之后）")
        else:
            print("  ❌ 停止按钮位置不正确")
            return False
        
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始停止按钮功能测试")
        print("=" * 50)
        
        tests = [
            ("停止按钮可见性", self.test_stop_button_visibility),
            ("启用/禁用功能", self.test_stop_button_enable_disable),
            ("任务管理功能", self.test_task_management),
            ("信号连接", self.test_stop_signal),
            ("UI布局", self.test_ui_layout),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 异常: {e}")
                logger.error(f"测试 {test_name} 时发生异常", exc_info=True)
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！停止按钮功能正常。")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关功能。")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.main_window:
            self.main_window.close()

def main():
    """主函数"""
    tester = StopButtonTester()
    
    try:
        success = tester.run_all_tests()
        
        # 如果所有测试通过，可以选择显示主窗口进行手动测试
        if success:
            print("\n🖱️  是否要显示主窗口进行手动测试？")
            print("   主窗口将显示5秒钟，您可以查看停止按钮的外观和位置。")
            
            if tester.main_window:
                tester.main_window.show()
                
                # 设置定时器自动关闭
                timer = QTimer()
                timer.timeout.connect(tester.main_window.close)
                timer.start(5000)  # 5秒后关闭
                
                # 运行事件循环
                tester.app.exec()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.error("测试失败", exc_info=True)
        return 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
