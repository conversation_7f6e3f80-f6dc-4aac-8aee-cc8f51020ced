#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口模块
程序的主界面，包含分镜列表和各种功能按钮。
"""

import os
import sys
import time
import logging
import json
from pathlib import Path

from PySide6.QtWidgets import (
    QMainWindow, QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QComboBox, QPushButton, QStatusBar, QMessageBox, QFileDialog,
    QSplashScreen
)
from PySide6.QtCore import Qt, Slot
from PySide6.QtGui import QAction, QIcon, QFont

# 导入自定义模块
from config import ConfigManager
from ui.theme_manager import ThemeManager
from ui.dialogs.screenplay_dialog import ScreenplayDialog
from ui.dialogs.model_settings import ModelSettingsDialog
from ui.dialogs.general_settings import GeneralSettingsDialog
from ui.dialogs.prompt_settings import PromptSettingsDialog
from ui.dialogs.voice_settings import VoiceSettingsDialog

from ui.widgets.shot_list import ShotListWidget
from ui.title_bar import TitleBar
from modules.ai_model import AIModel
from modules.audio import AudioProcessor
from modules.download import VideoDownloader
from modules.video import VideoProcessor
from modules.subtitle import SubtitleProcessor
from modules.utils import WorkerThread, ProgressWorkerThread
from modules.image_downloader import ImageDownloader

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("MainWindow")


class MainWindow(QMainWindow):
    """
    主窗口类
    程序的主界面，包含分镜列表和各种功能按钮
    """

    def __init__(self, config_manager=None):
        """
        初始化主窗口

        Args:
            config_manager: 配置管理器实例，如果为None则创建一个新的实例
        """
        super().__init__()

        # 配置管理器
        self.config_manager = config_manager or ConfigManager()

        # 获取启动画面实例（如果存在）
        splash = self._get_splash_screen()

        # 主题管理器
        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化主题...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self.theme_manager = ThemeManager(self.config_manager)

        # 初始化模块
        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化AI模型...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self.ai_model = AIModel(self.config_manager)

        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化音频处理器...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self.audio_processor = AudioProcessor(self.config_manager)

        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化视频下载器...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self.video_downloader = VideoDownloader(self.config_manager)

        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化视频处理器...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self.video_processor = VideoProcessor(self.config_manager)

        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化字幕处理器...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self.subtitle_processor = SubtitleProcessor(self.config_manager)

        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化图片下载器...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        # 初始化图片下载器
        self.image_downloader = ImageDownloader(self.config_manager)

        # 分镜数据
        self.shots = []

        # 设置状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 设置窗口
        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在设置用户界面...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self._setup_ui()

        # 应用主题
        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在应用主题...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self.theme_manager.apply_theme(QApplication.instance())

        # 设置标题栏主题
        current_theme = self.theme_manager.get_current_theme()
        is_dark_theme = (current_theme == "dark")
        self.title_bar.set_theme(is_dark_theme)

        # 设置状态栏初始消息
        self.status_bar.showMessage("就绪")

        # 加载AI模型
        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在加载AI模型列表...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self._update_model_list()

        # 加载最近的项目文件
        if splash:
            splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在加载最近的项目...</div>",
                              Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
            QApplication.processEvents()

        self._load_last_project()

    def _get_splash_screen(self):
        """
        获取当前活动的启动画面实例（如果存在）

        Returns:
            QSplashScreen: 启动画面实例，如果不存在则返回None
        """
        # 获取应用程序实例
        app = QApplication.instance()

        # 遍历所有顶级窗口
        for widget in app.topLevelWidgets():
            # 检查是否是启动画面
            if isinstance(widget, QSplashScreen):
                # 确保使用相同的字体
                font = QFont("Microsoft YaHei UI", 12, QFont.Bold)
                widget.setFont(font)
                return widget

        return None

    def _setup_ui(self):
        """设置UI"""
        # 设置窗口标题和大小
        self.setWindowTitle("AI视频解说制作软件")
        self.resize(1200, 800)

        # 设置窗口图标
        icon_path = Path("resources/icons/app.png")
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))

        # 设置无边框窗口
        self.setWindowFlags(Qt.FramelessWindowHint)

        # 允许自定义窗口背景
        self.setAttribute(Qt.WA_TranslucentBackground, False)

        # 创建自定义标题栏
        self.title_bar = TitleBar(self)
        self.title_bar.set_title(self.windowTitle())
        if icon_path.exists():
            self.title_bar.set_icon(QIcon(str(icon_path)))

        # 连接标题栏信号
        self.title_bar.minimizeClicked.connect(self.showMinimized)
        self.title_bar.maximizeClicked.connect(self._toggle_maximize)
        self.title_bar.closeClicked.connect(self.close)

        # 连接功能按钮信号
        self.title_bar.openProjectClicked.connect(self._on_open_project)
        self.title_bar.saveProjectClicked.connect(self._on_save_project)
        self.title_bar.settingsClicked.connect(self._on_settings_clicked)
        self.title_bar.themeChanged.connect(self._on_theme_changed)
        self.title_bar.aboutClicked.connect(self._on_about)

        # 隐藏菜单栏
        self.menuBar().setVisible(False)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 添加标题栏
        main_layout.addWidget(self.title_bar)

        # 内容布局
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(10)

        # 顶部工具栏布局
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(4)  # 更进一步减小按钮之间的间距
        toolbar_layout.setContentsMargins(0, 2, 0, 2)  # 减小上下边距

        # AI模型选择下拉框
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(120)  # 增加最小宽度
        self.model_combo.setMaximumWidth(150)  # 增加最大宽度
        self.model_combo.setMinimumHeight(28)  # 减小最小高度与按钮一致
        self.model_combo.setMaximumHeight(28)  # 减小最大高度与按钮一致
        # 设置下拉框样式，使其与按钮风格一致
        combo_style = """
            QComboBox {
                min-height: 28px;
                max-height: 28px;
                padding: 2px 6px;
                font-size: 13px;
                border-radius: 3px;
            }
        """
        self.model_combo.setStyleSheet(combo_style)
        self.model_combo.currentIndexChanged.connect(self._on_model_changed)
        toolbar_layout.addWidget(self.model_combo)

        # 设置按钮样式
        button_style = """
            QPushButton {
                min-width: 70px;
                max-width: 90px;
                min-height: 28px;
                max-height: 28px;
                padding: 2px 6px;
                font-size: 13px;
                font-weight: bold;
                border-radius: 3px;
            }
        """

        # 功能按钮
        self.btn_screenplay = QPushButton("文案分镜")
        self.btn_screenplay.clicked.connect(self._on_screenplay_clicked)
        self.btn_screenplay.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.btn_screenplay)

        self.btn_translate = QPushButton("一键翻译")
        self.btn_translate.clicked.connect(self._on_translate_clicked)
        self.btn_translate.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.btn_translate)

        self.btn_voice = QPushButton("AI配音")
        self.btn_voice.clicked.connect(self._on_voice_clicked)
        self.btn_voice.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.btn_voice)

        # 添加AI生图按钮
        self.btn_image_gen = QPushButton("AI生图")
        self.btn_image_gen.clicked.connect(self._on_image_gen_clicked)  # 添加占位回调函数
        self.btn_image_gen.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.btn_image_gen)

        # 深度解析按钮
        self.btn_analyze = QPushButton("深度解析")
        self.btn_analyze.clicked.connect(self._on_analyze_clicked)
        self.btn_analyze.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.btn_analyze)

        self.btn_download = QPushButton("批量下载")
        self.btn_download.clicked.connect(self._on_download_clicked)
        self.btn_download.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.btn_download)

        self.btn_generate = QPushButton("生成视频")
        self.btn_generate.clicked.connect(self._on_generate_clicked)
        self.btn_generate.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.btn_generate)

        self.btn_clear = QPushButton("清空列表")
        self.btn_clear.clicked.connect(self._on_clear_clicked)
        self.btn_clear.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.btn_clear)

        # 添加到内容布局
        content_layout.addLayout(toolbar_layout)

        # 分镜列表
        self.shot_list = ShotListWidget()
        content_layout.addWidget(self.shot_list)

        # 将内容布局添加到主布局
        main_layout.addLayout(content_layout)

        # 初始化UI状态
        self._update_ui_state()

    def _create_menus(self):
        """创建菜单栏"""
        # 菜单栏
        menubar = self.menuBar()

        # 设置菜单栏字体
        menu_font = QFont("Microsoft YaHei UI", 10, QFont.Bold)
        menubar.setFont(menu_font)

        # 文件菜单
        file_menu = menubar.addMenu("文件")

        open_action = QAction("打开项目", self)
        open_action.triggered.connect(self._on_open_project)
        file_menu.addAction(open_action)

        save_action = QAction("保存项目", self)
        save_action.triggered.connect(self._on_save_project)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设置菜单
        settings_menu = menubar.addMenu("系统设置")

        model_settings_action = QAction("AI模型设置", self)
        model_settings_action.triggered.connect(self._on_model_settings)
        settings_menu.addAction(model_settings_action)

        general_settings_action = QAction("常用设置", self)
        general_settings_action.triggered.connect(self._on_general_settings)
        settings_menu.addAction(general_settings_action)

        prompt_settings_action = QAction("提示词设置", self)
        prompt_settings_action.triggered.connect(self._on_prompt_settings)
        settings_menu.addAction(prompt_settings_action)

        # 主题菜单
        theme_menu = menubar.addMenu("主题切换")

        light_theme_action = QAction("浅色主题", self)
        light_theme_action.triggered.connect(lambda: self._on_theme_changed("light"))
        theme_menu.addAction(light_theme_action)

        dark_theme_action = QAction("暗色主题", self)
        dark_theme_action.triggered.connect(lambda: self._on_theme_changed("dark"))
        theme_menu.addAction(dark_theme_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助")

        about_action = QAction("关于", self)
        about_action.triggered.connect(self._on_about)
        help_menu.addAction(about_action)

    def _update_model_list(self):
        """更新AI模型列表"""
        # 清空下拉框
        self.model_combo.clear()

        # 获取模型列表
        models = self.config_manager.get("ai_models", [])
        current_model = self.config_manager.get("current_model", "")

        # 添加到下拉框
        for model in models:
            name = model.get("name", "")
            if name:
                self.model_combo.addItem(name)

        # 设置当前模型
        if current_model:
            index = self.model_combo.findText(current_model)
            if index >= 0:
                self.model_combo.setCurrentIndex(index)

    def _update_ui_state(self):
        """更新UI状态"""
        # 根据分镜数据更新按钮状态
        has_shots = len(self.shots) > 0

        # 更新按钮状态
        self.btn_translate.setEnabled(has_shots)
        self.btn_voice.setEnabled(has_shots)
        self.btn_image_gen.setEnabled(True)  # AI生图按钮始终启用
        self.btn_analyze.setEnabled(has_shots)
        self.btn_download.setEnabled(has_shots)
        self.btn_generate.setEnabled(has_shots)
        self.btn_clear.setEnabled(has_shots)

        # 更新状态栏
        if has_shots:
            self.status_bar.showMessage(f"已载入 {len(self.shots)} 个分镜")
        else:
            self.status_bar.showMessage("就绪")

    @Slot()
    def _on_model_changed(self):
        """处理模型选择变更"""
        # 获取当前选择的模型
        model_name = self.model_combo.currentText()
        if not model_name:
            return

        # 更新配置
        self.config_manager.set("current_model", model_name)
        self.config_manager.save_config()

        # 切换模型
        self.ai_model.change_model(model_name)

        # 更新状态栏
        self.status_bar.showMessage(f"已选择模型: {model_name}")

    @Slot()
    def _on_screenplay_clicked(self):
        """处理文案分镜按钮点击"""
        # 创建文案分镜对话框
        dialog = ScreenplayDialog(
            self.config_manager,
            self.ai_model,
            parent=self
        )

        # 连接信号
        dialog.shots_imported.connect(self._on_shots_imported)

        # 显示对话框
        dialog.exec()

    @Slot(list)
    def _on_shots_imported(self, shots):
        """
        处理分镜导入

        Args:
            shots: 分镜列表
        """
        if not shots:
            return

        # 记录导入信息
        logger.info(f"接收到 {len(shots)} 个分镜")

        # 确保shots是一个列表
        if not isinstance(shots, list):
            logger.warning(f"接收到的shots不是列表，而是 {type(shots)}")
            shots = [shots] if shots else []

        # 更新分镜数据
        self.shots = shots.copy()  # 创建副本，避免引用问题

        # 记录分镜信息
        for i, shot in enumerate(self.shots):
            logger.info(f"分镜 {i+1}: ID={shot.get('id')}, 文本长度={len(shot.get('text', ''))}")

        # 更新分镜列表
        self.shot_list.set_shots(self.shots)

        # 更新UI状态
        self._update_ui_state()

        # 更新状态栏
        self.status_bar.showMessage(f"已导入 {len(self.shots)} 个分镜")

    @Slot()
    def _on_translate_clicked(self):
        """处理一键翻译按钮点击"""
        # 检查是否有分镜
        if not self.shots:
            QMessageBox.warning(self, "警告", "没有分镜可以翻译。")
            return

        # 确认翻译
        reply = QMessageBox.question(
            self,
            "确认翻译",
            f"将使用 {self.model_combo.currentText()} 模型翻译 {len(self.shots)} 个分镜的提示词和标签。\n\n是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return

        # 禁用UI
        self._set_ui_enabled(False)

        # 更新状态栏
        self.status_bar.showMessage("正在翻译...")

        # 创建进度对话框
        progress_dialog = QMessageBox(self)
        progress_dialog.setWindowTitle("翻译进度")
        progress_dialog.setText("正在翻译中，请稍候...")
        progress_dialog.setStandardButtons(QMessageBox.NoButton)
        progress_dialog.setModal(True)  # 设置为模态对话框
        progress_dialog.show()

        # 创建工作线程，使用优化的批量翻译方法
        worker = ProgressWorkerThread(
            target=self.ai_model.batch_translate_optimized,
            args=(self.shots,),
            kwargs={"batch_size": 5}  # 每批处理5个分镜，减小批次大小以提高成功率
        )

        # 连接信号
        worker.progress.connect(lambda current, total, status: self._update_translate_progress(current, total, status, progress_dialog))
        worker.completed.connect(lambda result: self._on_translate_completed(result, progress_dialog))
        worker.error.connect(lambda error: self._on_translate_error(error, progress_dialog))

        # 启动线程
        worker.start()

    def _update_translate_progress(self, current, total, status, dialog):
        """
        更新翻译进度

        Args:
            current: 当前进度
            total: 总进度
            status: 状态信息
            dialog: 进度对话框
        """
        # 更新进度对话框
        progress_percent = int(current / total * 100) if total > 0 else 0

        # 如果有状态信息，显示状态信息
        if status:
            dialog.setText(f"正在翻译中，请稍候...\n\n进度: {progress_percent}% ({current}/{total})\n状态: {status}")
            # 更新状态栏
            self.status_bar.showMessage(f"正在翻译... {progress_percent}% - {status}")
        else:
            dialog.setText(f"正在翻译中，请稍候...\n\n进度: {progress_percent}% ({current}/{total})")
            # 更新状态栏
            self.status_bar.showMessage(f"正在翻译... {progress_percent}%")

    def _on_translate_completed(self, result, dialog):
        """
        翻译完成回调

        Args:
            result: 翻译结果
            dialog: 进度对话框
        """
        # 关闭进度对话框
        try:
            dialog.done(0)  # 使用done而不是close，确保对话框被正确关闭
        except Exception as e:
            logger.error(f"关闭翻译进度对话框失败: {e}")

        # 更新分镜数据
        self.shots = result

        # 更新分镜列表
        self.shot_list.set_shots(self.shots)

        # 启用UI
        self._set_ui_enabled(True)

        # 更新状态栏
        self.status_bar.showMessage(f"翻译完成，共 {len(self.shots)} 个分镜")

        # 显示完成消息
        QMessageBox.information(self, "翻译完成", f"已完成 {len(self.shots)} 个分镜的翻译。")

    def _on_translate_error(self, error, dialog):
        """
        翻译错误回调

        Args:
            error: 错误信息
            dialog: 进度对话框
        """
        # 关闭进度对话框
        try:
            dialog.done(0)  # 使用done而不是close，确保对话框被正确关闭
        except Exception as e:
            logger.error(f"关闭翻译进度对话框失败: {e}")

        # 启用UI
        self._set_ui_enabled(True)

        # 更新状态栏
        self.status_bar.showMessage(f"翻译失败: {error}")

        # 显示错误消息
        QMessageBox.critical(self, "翻译失败", f"翻译过程中发生错误:\n\n{error}")

    @Slot()
    def _on_voice_clicked(self):
        """处理AI配音按钮点击"""
        # 检查是否有分镜
        if not self.shots:
            QMessageBox.warning(self, "警告", "没有分镜可以配音。")
            return

        # 创建配音设置对话框
        dialog = VoiceSettingsDialog(
            self.config_manager,
            self.audio_processor,
            self.shots,
            parent=self
        )

        # 连接信号
        dialog.shots_updated.connect(self._on_shots_updated)

        # 显示对话框
        dialog.exec()

    def _download_images(self, shots, progress_callback=None):
        """
        图片下载功能，根据提示词搜索并下载相关图片

        Args:
            shots: 分镜列表
            progress_callback: 进度回调函数，接收参数(current, total, status)

        Returns:
            List[Dict]: 更新后的分镜列表
        """
        # 创建一个副本，避免修改原始数据
        result_shots = shots.copy()
        total = len(shots)

        # 获取图片下载设置
        image_settings = self.config_manager.get("general_settings.image", {})
        image_dir = image_settings.get("image_dir", "./images")

        # 确保图片目录存在
        os.makedirs(image_dir, exist_ok=True)

        # 使用ImageDownloader批量下载图片
        return self.image_downloader.batch_download_for_shots(
            result_shots,
            image_dir,
            progress_callback
        )

    @Slot()
    def _on_image_gen_clicked(self):
        """处理AI生图按钮点击"""
        # 检查是否有分镜
        if not self.shots:
            # 如果没有分镜，提示用户先创建分镜
            reply = QMessageBox.question(
                self,
                "提示",
                "没有分镜可以生成图片。是否先创建分镜？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 调用文案分镜功能
                self._on_screenplay_clicked()
            return

        # 检查是否有英文提示词
        has_en_prompt = all('en_prompt' in shot and shot['en_prompt'] for shot in self.shots)

        if not has_en_prompt:
            reply = QMessageBox.question(
                self,
                "提示",
                "部分分镜没有英文提示词，建议先进行翻译。\n\n是否继续？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

        # 确认生成图片
        reply = QMessageBox.question(
            self,
            "确认生成图片",
            f"将为 {len(self.shots)} 个分镜搜索并下载相关图片。\n\n是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return

        # 禁用UI
        self._set_ui_enabled(False)

        # 更新状态栏
        self.status_bar.showMessage("正在生成图片...")

        # 创建进度对话框
        progress_dialog = QMessageBox(self)
        progress_dialog.setWindowTitle("图片生成进度")
        progress_dialog.setText("正在搜索并下载图片，请稍候...")
        progress_dialog.setStandardButtons(QMessageBox.NoButton)
        progress_dialog.show()

        # 创建工作线程
        worker = ProgressWorkerThread(
            target=self._download_images,
            args=(self.shots,)
        )

        # 连接信号
        worker.progress.connect(lambda current, total, status: self._update_image_progress(current, total, status, progress_dialog))
        worker.completed.connect(lambda result: self._on_image_completed(result, progress_dialog))
        worker.error.connect(lambda error: self._on_image_error(error, progress_dialog))

        # 启动线程
        worker.start()

    def _update_image_progress(self, current, total, status, dialog):
        """
        更新图片生成进度

        Args:
            current: 当前进度
            total: 总进度
            status: 状态信息
            dialog: 进度对话框
        """
        # 更新进度对话框
        progress_percent = int(current / total * 100) if total > 0 else 0
        dialog.setText(f"正在搜索并下载图片，请稍候...\n\n进度: {progress_percent}% ({current}/{total})\n状态: {status}")

        # 更新状态栏
        self.status_bar.showMessage(f"正在生成图片... {progress_percent}%")

    def _on_image_completed(self, result, dialog):
        """
        图片生成完成回调

        Args:
            result: 生成结果
            dialog: 进度对话框
        """
        # 关闭进度对话框
        try:
            dialog.done(0)  # 使用done而不是close，确保对话框被正确关闭
        except Exception as e:
            logger.error(f"关闭图片生成进度对话框失败: {e}")

        # 更新分镜数据
        self.shots = result

        # 更新分镜列表
        self.shot_list.set_shots(self.shots)

        # 启用UI
        self._set_ui_enabled(True)

        # 统计找到图片的分镜数量
        image_count = sum(1 for shot in self.shots if shot.get('image_path'))

        # 更新状态栏
        self.status_bar.showMessage(f"图片生成完成，找到 {image_count}/{len(self.shots)} 个图片")

        # 显示完成消息
        QMessageBox.information(self, "图片生成完成",
                               f"已完成 {len(self.shots)} 个分镜的图片生成：\n"
                               f"- 找到 {image_count} 个图片")

    def _on_image_error(self, error, dialog):
        """
        图片生成错误回调

        Args:
            error: 错误信息
            dialog: 进度对话框
        """
        # 关闭进度对话框
        try:
            dialog.done(0)  # 使用done而不是close，确保对话框被正确关闭
        except Exception as e:
            logger.error(f"关闭图片生成进度对话框失败: {e}")

        # 启用UI
        self._set_ui_enabled(True)

        # 更新状态栏
        self.status_bar.showMessage(f"图片生成失败: {error}")

        # 显示错误消息
        QMessageBox.critical(self, "图片生成失败", f"图片生成过程中发生错误:\n\n{error}")

    @Slot()
    def _on_analyze_clicked(self):
        """处理深度解析按钮点击"""
        # 检查是否有分镜
        if not self.shots:
            QMessageBox.warning(self, "警告", "没有分镜可以解析。")
            return

        # 检查是否有英文提示词
        has_en_prompt = all('en_prompt' in shot and shot['en_prompt'] for shot in self.shots)

        if not has_en_prompt:
            reply = QMessageBox.question(
                self,
                "提示",
                "部分分镜没有英文提示词，建议先进行翻译。\n\n是否继续？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

        # 确认解析
        reply = QMessageBox.question(
            self,
            "确认解析",
            f"将对 {len(self.shots)} 个分镜进行解析，查找合适的视频和图片。\n\n是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return

        # 禁用UI
        self._set_ui_enabled(False)

        # 更新状态栏
        self.status_bar.showMessage("正在解析...")

        # 创建进度对话框
        progress_dialog = QMessageBox(self)
        progress_dialog.setWindowTitle("解析进度")
        progress_dialog.setText("正在解析中，请稍候...")
        progress_dialog.setStandardButtons(QMessageBox.NoButton)
        progress_dialog.show()

        # 创建工作线程
        worker = ProgressWorkerThread(
            target=self._search_videos,
            args=(self.shots,)
        )

        # 连接信号
        worker.progress.connect(lambda current, total, status: self._update_analyze_progress(current, total, status, progress_dialog))
        worker.completed.connect(lambda result: self._on_analyze_completed(result, progress_dialog))
        worker.error.connect(lambda error: self._on_analyze_error(error, progress_dialog))

        # 启动线程
        worker.start()

    def _search_videos(self, shots, progress_callback=None):
        """
        视频搜索功能，根据提示词搜索相关视频和图片

        Args:
            shots: 分镜列表
            progress_callback: 进度回调函数，接收参数(current, total, status)

        Returns:
            List[Dict]: 更新后的分镜列表
        """
        # 创建一个副本，避免修改原始数据
        result_shots = shots.copy()
        total = len(shots)

        # 获取图片下载设置
        image_settings = self.config_manager.get("general_settings.image", {})
        image_dir = image_settings.get("image_dir", "./images")

        # 确保图片目录存在
        os.makedirs(image_dir, exist_ok=True)

        for i, shot in enumerate(result_shots):
            try:
                # 准备搜索关键词
                en_prompt = shot.get("en_prompt", "")
                en_tags = shot.get("en_tags", "")
                prompt = shot.get("prompt", "")  # 中文提示词
                tags = shot.get("tags", "")      # 中文标签

                # 初始化搜索关键词
                search_query = en_prompt
                if en_tags:
                    # 将标签添加到搜索关键词
                    search_query = f"{search_query} {en_tags}"

                # 更新进度
                if progress_callback:
                    progress_callback(i, total, f"搜索视频中: {shot.get('id', i+1)}")

                # 搜索视频
                search_results = self.video_downloader.search_videos(search_query, max_results=5)

                # 如果搜索结果为空，尝试使用标签搜索
                if not search_results and en_tags:
                    search_results = self.video_downloader.search_videos(en_tags, max_results=5)

                # 如果仍然为空，尝试使用中文提示词搜索
                if not search_results:
                    if prompt:
                        search_results = self.video_downloader.search_videos(prompt, max_results=3)

                    if not search_results and tags:
                        search_results = self.video_downloader.search_videos(tags, max_results=3)

                # 如果找到视频，保存第一个结果
                if search_results:
                    # 保存视频URL
                    shot["video_url"] = search_results[0].get("url", "")
                    # 保存候选视频列表
                    shot["video_candidates"] = search_results

                    # 更新进度
                    if progress_callback:
                        progress_callback(i+1, total, f"找到视频: {shot.get('id', i+1)}")
                else:
                    # 更新进度
                    if progress_callback:
                        progress_callback(i+1, total, f"未找到视频: {shot.get('id', i+1)}")

                # 下载图片
                # 更新进度
                if progress_callback:
                    progress_callback(i+1, total, f"搜索图片中: {shot.get('id', i+1)}")

                # 为当前分镜创建专用目录
                shot_image_dir = os.path.join(image_dir, f"shot_{shot.get('id', i+1)}")

                # 使用ImageDownloader下载图片
                try:
                    # 准备搜索关键词
                    if search_query:
                        # 使用ImageDownloader下载图片
                        image_paths = self.image_downloader.search_and_download(
                            search_query,
                            shot_image_dir,
                            limit=1,  # 每个分镜只下载一张图片
                            progress_callback=lambda c, t, status: progress_callback(
                                i, total, f"分镜 {shot.get('id', i+1)}: {status}"
                            ) if progress_callback else None
                        )

                        # 如果找到图片，保存第一张图片路径
                        if image_paths:
                            shot["image_path"] = image_paths[0]
                            logger.info(f"成功下载图片: {image_paths[0]}")

                            # 更新进度
                            if progress_callback:
                                progress_callback(i+1, total, f"图片下载完成: {shot.get('id', i+1)}")
                        else:
                            logger.warning(f"未找到可下载的图片: {search_query}")
                            if progress_callback:
                                progress_callback(i+1, total, f"未找到图片: {shot.get('id', i+1)}")
                    else:
                        logger.warning(f"分镜 {shot.get('id', i+1)} 没有搜索关键词")
                        if progress_callback:
                            progress_callback(i+1, total, f"没有搜索关键词: {shot.get('id', i+1)}")
                except Exception as e:
                    logger.error(f"下载图片失败: {e}")
                    if progress_callback:
                        progress_callback(i+1, total, f"图片下载失败: {shot.get('id', i+1)} - {e}")
            except Exception as e:
                logger.error(f"搜索失败: {e}")
                # 更新进度
                if progress_callback:
                    progress_callback(i+1, total, f"搜索失败: {shot.get('id', i+1)} - {e}")

        return result_shots

    def _update_analyze_progress(self, current, total, status, dialog):
        """
        更新解析进度

        Args:
            current: 当前进度
            total: 总进度
            status: 状态信息
            dialog: 进度对话框
        """
        # 更新进度对话框
        progress_percent = int(current / total * 100) if total > 0 else 0
        dialog.setText(f"正在解析中，请稍候...\n\n进度: {progress_percent}% ({current}/{total})\n状态: {status}")

        # 更新状态栏
        self.status_bar.showMessage(f"正在解析... {progress_percent}%")

    def _on_analyze_completed(self, result, dialog):
        """
        解析完成回调

        Args:
            result: 解析结果
            dialog: 进度对话框
        """
        # 关闭进度对话框
        try:
            dialog.done(0)  # 使用done而不是close，确保对话框被正确关闭
        except Exception as e:
            logger.error(f"关闭解析进度对话框失败: {e}")

        # 更新分镜数据
        self.shots = result

        # 更新分镜列表
        self.shot_list.set_shots(self.shots)

        # 启用UI
        self._set_ui_enabled(True)

        # 统计找到视频和图片的分镜数量
        video_count = sum(1 for shot in self.shots if shot.get('video_url'))
        image_count = sum(1 for shot in self.shots if shot.get('image_path'))

        # 更新状态栏
        self.status_bar.showMessage(f"解析完成，找到 {video_count}/{len(self.shots)} 个视频，{image_count}/{len(self.shots)} 个图片")

        # 显示完成消息
        QMessageBox.information(self, "解析完成",
                               f"已完成 {len(self.shots)} 个分镜的解析：\n"
                               f"- 找到 {video_count} 个视频\n"
                               f"- 找到 {image_count} 个图片")

    def _on_analyze_error(self, error, dialog):
        """
        解析错误回调

        Args:
            error: 错误信息
            dialog: 进度对话框
        """
        # 关闭进度对话框
        try:
            dialog.done(0)  # 使用done而不是close，确保对话框被正确关闭
        except Exception as e:
            logger.error(f"关闭解析进度对话框失败: {e}")

        # 启用UI
        self._set_ui_enabled(True)

        # 更新状态栏
        self.status_bar.showMessage(f"解析失败: {error}")

        # 显示错误消息
        QMessageBox.critical(self, "解析失败", f"解析过程中发生错误:\n\n{error}")

    @Slot()
    def _on_download_clicked(self):
        """处理批量下载按钮点击"""
        # 检查是否有分镜
        if not self.shots:
            QMessageBox.warning(self, "警告", "没有分镜可以下载。")
            return

        # 检查是否有视频URL
        has_video_url = any('video_url' in shot and shot['video_url'] for shot in self.shots)

        if not has_video_url:
            reply = QMessageBox.question(
                self,
                "提示",
                "没有可下载的视频URL，是否先进行深度解析？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self._on_analyze_clicked()
                return
            else:
                return

        # 计算需要下载的视频数量
        download_count = sum(1 for shot in self.shots if shot.get('video_url'))

        # 确认下载
        reply = QMessageBox.question(
            self,
            "确认下载",
            f"将下载 {download_count} 个视频。\n\n是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return

        # 禁用UI
        self._set_ui_enabled(False)

        # 更新状态栏
        self.status_bar.showMessage("正在下载...")

        # 创建进度对话框
        progress_dialog = QMessageBox(self)
        progress_dialog.setWindowTitle("下载进度")
        progress_dialog.setText("正在下载中，请稍候...")
        progress_dialog.setStandardButtons(QMessageBox.NoButton)
        progress_dialog.show()

        # 创建工作线程
        worker = ProgressWorkerThread(
            target=self.video_downloader.batch_download,
            args=(self.shots,)
        )

        # 连接信号
        worker.progress.connect(lambda current, total, status: self._update_download_progress(current, total, status, progress_dialog))
        worker.completed.connect(lambda result: self._on_download_completed(result, progress_dialog))
        worker.error.connect(lambda error: self._on_download_error(error, progress_dialog))

        # 启动线程
        worker.start()

    def _update_download_progress(self, current, total, status, dialog):
        """
        更新下载进度

        Args:
            current: 当前进度
            total: 总进度
            status: 状态信息
            dialog: 进度对话框
        """
        # 更新进度对话框
        progress_percent = int(current / total * 100) if total > 0 else 0

        dialog.setText(f"正在下载中，请稍候...\n\n总进度: {progress_percent}% ({current}/{total})\n"
                      f"状态: {status}")

        # 更新状态栏
        self.status_bar.showMessage(f"正在下载... {progress_percent}%")

    def _on_download_completed(self, result, dialog):
        """
        下载完成回调

        Args:
            result: 下载结果
            dialog: 进度对话框
        """
        # 关闭进度对话框
        try:
            dialog.done(0)  # 使用done而不是close，确保对话框被正确关闭
        except Exception as e:
            logger.error(f"关闭下载进度对话框失败: {e}")

        # 更新分镜数据
        self.shots = result

        # 更新分镜列表
        self.shot_list.set_shots(self.shots)

        # 启用UI
        self._set_ui_enabled(True)

        # 统计下载成功的视频数量
        downloaded_count = sum(1 for shot in self.shots if shot.get('video_file'))

        # 更新状态栏
        self.status_bar.showMessage(f"下载完成，成功下载 {downloaded_count} 个视频")

        # 显示完成消息
        QMessageBox.information(self, "下载完成", f"已完成 {len(self.shots)} 个分镜的下载，成功下载 {downloaded_count} 个视频。")

    def _on_download_error(self, error, dialog):
        """
        下载错误回调

        Args:
            error: 错误信息
            dialog: 进度对话框
        """
        # 关闭进度对话框
        try:
            dialog.done(0)  # 使用done而不是close，确保对话框被正确关闭
        except Exception as e:
            logger.error(f"关闭下载进度对话框失败: {e}")

        # 启用UI
        self._set_ui_enabled(True)

        # 更新状态栏
        self.status_bar.showMessage(f"下载失败: {error}")

        # 显示错误消息
        QMessageBox.critical(self, "下载失败", f"下载过程中发生错误:\n\n{error}")

    @Slot()
    def _on_generate_clicked(self):
        """处理生成视频按钮点击"""
        # 检查是否有分镜
        if not self.shots:
            QMessageBox.warning(self, "警告", "没有分镜可以生成视频。")
            return

        # 检查是否有视频文件
        has_video_file = any('video_file' in shot and shot['video_file'] and os.path.exists(shot['video_file']) for shot in self.shots)

        if not has_video_file:
            QMessageBox.warning(self, "警告", "没有可用的视频文件，请先下载视频。")
            return

        # 选择输出文件
        output_dir = self.config_manager.get("general_settings.output.output_dir", "./output")
        output_file, _ = QFileDialog.getSaveFileName(
            self,
            "保存视频",
            os.path.join(output_dir, f"video_{int(time.time())}.mp4"),
            "视频文件 (*.mp4 *.avi *.mkv);;所有文件 (*.*)"
        )

        if not output_file:
            return

        # 视频生成选项
        options = {
            "add_subtitles": True,
            "use_audio": True,
            "add_bgm": False,
            "bgm_file": None
        }

        # 确认生成
        reply = QMessageBox.question(
            self,
            "确认生成",
            f"将使用当前设置生成视频，并保存到:\n{output_file}\n\n是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply != QMessageBox.Yes:
            return

        # 禁用UI
        self._set_ui_enabled(False)

        # 更新状态栏
        self.status_bar.showMessage("正在生成视频...")

        # 创建进度对话框
        progress_dialog = QMessageBox(self)
        progress_dialog.setWindowTitle("生成视频进度")
        progress_dialog.setText("正在生成视频中，请稍候...")
        progress_dialog.setStandardButtons(QMessageBox.NoButton)
        progress_dialog.show()

        # 创建工作线程
        worker = ProgressWorkerThread(
            target=self.video_processor.generate_video,
            args=(self.shots, output_file),
            kwargs=options
        )

        # 连接信号
        worker.progress.connect(lambda progress, total, status: self._update_generate_progress(progress, total, status, progress_dialog))
        worker.completed.connect(lambda result: self._on_generate_completed(result, output_file, progress_dialog))
        worker.error.connect(lambda error: self._on_generate_error(error, progress_dialog))

        # 启动线程
        worker.start()

    def _update_generate_progress(self, progress, total, status, dialog):
        """
        更新生成视频进度

        Args:
            progress: 当前进度
            total: 总进度
            status: 状态信息
            dialog: 进度对话框
        """
        # 更新进度对话框
        progress_percent = int(progress / total * 100) if total > 0 else 0
        dialog.setText(f"正在生成视频中，请稍候...\n\n进度: {progress_percent}%\n状态: {status}")

        # 更新状态栏
        self.status_bar.showMessage(f"正在生成视频... {progress_percent}%")

    def _on_generate_completed(self, result, output_file, dialog):
        """
        生成视频完成回调

        Args:
            result: 生成结果
            output_file: 输出文件路径
            dialog: 进度对话框
        """
        # 关闭进度对话框
        dialog.close()

        # 启用UI
        self._set_ui_enabled(True)

        # 检查结果
        if result:
            # 更新状态栏
            self.status_bar.showMessage(f"视频生成完成，已保存到: {output_file}")

            # 显示完成消息
            reply = QMessageBox.information(
                self,
                "生成完成",
                f"视频已成功生成并保存到:\n{output_file}\n\n是否打开输出目录？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            # 打开输出目录
            if reply == QMessageBox.Yes:
                # 打开输出目录
                output_dir = os.path.dirname(output_file)
                os.startfile(output_dir) if sys.platform == 'win32' else os.system(f'xdg-open "{output_dir}"')
        else:
            # 更新状态栏
            self.status_bar.showMessage("视频生成失败")

            # 显示错误消息
            QMessageBox.critical(self, "生成失败", "视频生成失败，请检查日志。")

    def _on_generate_error(self, error, dialog):
        """
        生成视频错误回调

        Args:
            error: 错误信息
            dialog: 进度对话框
        """
        # 关闭进度对话框
        dialog.close()

        # 启用UI
        self._set_ui_enabled(True)

        # 更新状态栏
        self.status_bar.showMessage(f"生成视频失败: {error}")

        # 显示错误消息
        QMessageBox.critical(self, "生成失败", f"生成视频过程中发生错误:\n\n{error}")

    @Slot()
    def _on_clear_clicked(self):
        """处理清空列表按钮点击"""
        # 确认清空
        reply = QMessageBox.question(
            self,
            "确认清空",
            f"将清空当前的 {len(self.shots)} 个分镜。\n\n是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 清空分镜数据
        self.shots = []

        # 更新分镜列表
        self.shot_list.clear_shots()

        # 更新UI状态
        self._update_ui_state()

        # 更新状态栏
        self.status_bar.showMessage("已清空分镜列表")

    @Slot()
    def _load_last_project(self):
        """加载最近的项目文件"""
        # 获取最近的项目文件路径
        last_project = self.config_manager.get("general_settings.recent_files.last_project", "")

        # 如果没有最近的项目文件，则返回
        if not last_project or not os.path.exists(last_project):
            return

        # 加载项目文件
        try:
            with open(last_project, 'r', encoding='utf-8') as f:
                project_data = json.load(f)

            # 检查项目数据
            if not isinstance(project_data, dict) or 'shots' not in project_data:
                logger.warning(f"无效的项目文件: {last_project}")
                return

            # 检查分镜数据
            shots = project_data.get('shots', [])
            if not shots:
                logger.warning(f"项目不包含任何分镜: {last_project}")
                return

            # 更新分镜数据
            self.shots = shots

            # 更新分镜列表
            self.shot_list.set_shots(self.shots)

            # 更新UI状态
            self._update_ui_state()

            # 更新状态栏
            self.status_bar.showMessage(f"已加载上次项目文件: {last_project}")

            logger.info(f"已自动加载上次项目文件: {last_project}")
        except Exception as e:
            logger.error(f"加载上次项目文件失败: {e}")
            # 不显示错误消息，静默失败

    def _on_open_project(self):
        """处理打开项目菜单点击"""
        # 选择项目文件
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "打开项目",
            "",
            "项目文件 (*.json);;所有文件 (*.*)"
        )

        if not file_path:
            return

        # 加载项目文件
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)

            # 检查项目数据
            if not isinstance(project_data, dict) or 'shots' not in project_data:
                QMessageBox.warning(self, "警告", "无效的项目文件。")
                return

            # 检查分镜数据
            shots = project_data.get('shots', [])
            if not shots:
                QMessageBox.warning(self, "警告", "项目不包含任何分镜。")
                return

            # 更新分镜数据
            self.shots = shots

            # 更新分镜列表
            self.shot_list.set_shots(self.shots)

            # 更新UI状态
            self._update_ui_state()

            # 保存最近打开的项目文件路径
            self.config_manager.set("general_settings.recent_files.last_project", file_path)
            self.config_manager.save_config()

            # 更新状态栏
            self.status_bar.showMessage(f"已加载项目文件: {file_path}")
        except Exception as e:
            logger.error(f"加载项目文件失败: {e}")
            QMessageBox.critical(self, "错误", f"加载项目文件失败:\n\n{e}")

    @Slot()
    def _on_save_project(self):
        """处理保存项目菜单点击"""
        # 检查是否有分镜
        if not self.shots:
            QMessageBox.warning(self, "警告", "没有分镜可以保存。")
            return

        # 选择保存文件
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存项目",
            "",
            "项目文件 (*.json);;所有文件 (*.*)"
        )

        if not file_path:
            return

        # 保存项目文件
        try:
            # 准备项目数据
            project_data = {
                'shots': self.shots,
                'version': '1.0',
                'timestamp': int(time.time())
            }

            # 保存项目文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=4)

            # 保存最近打开的项目文件路径
            self.config_manager.set("general_settings.recent_files.last_project", file_path)
            self.config_manager.save_config()

            # 更新状态栏
            self.status_bar.showMessage(f"已保存项目文件: {file_path}")

            # 显示消息
            QMessageBox.information(self, "保存成功", f"项目已保存到:\n{file_path}")
        except Exception as e:
            logger.error(f"保存项目文件失败: {e}")
            QMessageBox.critical(self, "错误", f"保存项目文件失败:\n\n{e}")

    @Slot()
    def _on_model_settings(self):
        """处理AI模型设置菜单点击"""
        # 创建模型设置对话框
        dialog = ModelSettingsDialog(self.config_manager, self.ai_model, parent=self)

        # 连接信号
        dialog.settings_updated.connect(self._on_settings_updated)

        # 显示对话框
        dialog.exec()

    @Slot()
    def _on_general_settings(self):
        """处理常用设置菜单点击"""
        # 创建常用设置对话框
        dialog = GeneralSettingsDialog(self.config_manager, parent=self)

        # 连接信号
        dialog.settings_updated.connect(self._on_settings_updated)

        # 显示对话框
        dialog.exec()

    @Slot()
    def _on_prompt_settings(self):
        """处理提示词设置菜单点击"""
        # 创建提示词设置对话框
        dialog = PromptSettingsDialog(self.config_manager, parent=self)

        # 连接信号
        dialog.settings_updated.connect(self._on_settings_updated)

        # 显示对话框
        dialog.exec()

    @Slot(str)
    def _on_theme_changed(self, theme):
        """
        处理主题变更

        Args:
            theme: 主题名称
        """
        # 切换主题
        self.theme_manager.set_theme(theme, QApplication.instance())

        # 更新标题栏主题
        is_dark_theme = (theme == "dark")
        self.title_bar.set_theme(is_dark_theme)

        # 更新状态栏
        self.status_bar.showMessage(f"已切换到{theme}主题")

    @Slot(str)
    def _on_settings_clicked(self, settings_type):
        """
        处理设置按钮点击

        Args:
            settings_type: 设置类型
        """
        if settings_type == "model":
            self._on_model_settings()
        elif settings_type == "general":
            self._on_general_settings()
        elif settings_type == "prompt":
            self._on_prompt_settings()

    @Slot()
    def _on_about(self):
        """处理关于菜单点击"""
        QMessageBox.about(
            self,
            "关于",
            "AI视频解说制作软件\n\n"
            "版本: 1.0.0\n\n"
            "使用PySide6、Edge-TTS、YouTube-DLP等技术开发"
        )

    @Slot()
    def _on_settings_updated(self):
        """处理设置更新"""
        # 更新AI模型列表
        self._update_model_list()

        # 更新模块
        self.audio_processor = AudioProcessor(self.config_manager)
        self.video_downloader = VideoDownloader(self.config_manager)
        self.video_processor = VideoProcessor(self.config_manager)
        self.subtitle_processor = SubtitleProcessor(self.config_manager)

        # 更新状态栏
        self.status_bar.showMessage("设置已更新")

    @Slot(list)
    def _on_shots_updated(self, shots):
        """
        处理分镜更新

        Args:
            shots: 分镜列表
        """
        # 更新分镜数据
        self.shots = shots

        # 更新分镜列表
        self.shot_list.set_shots(self.shots)

        # 更新UI状态
        self._update_ui_state()

        # 更新状态栏
        self.status_bar.showMessage(f"已更新 {len(self.shots)} 个分镜")

    def _set_ui_enabled(self, enabled):
        """
        设置UI启用状态

        Args:
            enabled: 是否启用
        """
        # 设置按钮状态
        self.btn_screenplay.setEnabled(enabled)
        self.btn_translate.setEnabled(enabled and len(self.shots) > 0)
        self.btn_voice.setEnabled(enabled and len(self.shots) > 0)
        self.btn_analyze.setEnabled(enabled and len(self.shots) > 0)
        self.btn_download.setEnabled(enabled and len(self.shots) > 0)
        self.btn_generate.setEnabled(enabled and len(self.shots) > 0)
        self.btn_clear.setEnabled(enabled and len(self.shots) > 0)

        # 设置下拉框状态
        self.model_combo.setEnabled(enabled)

        # 设置标题栏功能按钮状态
        self.title_bar.open_button.setEnabled(enabled)
        self.title_bar.save_button.setEnabled(enabled)
        self.title_bar.settings_button.setEnabled(enabled)
        self.title_bar.theme_button.setEnabled(enabled)
        self.title_bar.about_button.setEnabled(enabled)

    def _toggle_maximize(self):
        """
        切换窗口最大化/还原状态
        """
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()