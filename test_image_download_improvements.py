#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片下载改进测试脚本
测试新的图片下载方法和备用API的效果
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ConfigManager
from modules.image_downloader import ImageDownloader

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ImageDownloadTest")

class ImageDownloadTester:
    """图片下载改进测试器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.downloader = None
        
    def test_alternative_apis(self):
        """测试备用图片API"""
        print("🌐 测试备用图片API...")
        
        try:
            self.downloader = ImageDownloader(self.config_manager)
            
            # 测试Lorem Picsum
            print("  📸 测试Lorem Picsum...")
            picsum_urls = self.downloader._get_picsum_images(2, 800, 600)
            if picsum_urls:
                print(f"    ✅ Lorem Picsum成功获取 {len(picsum_urls)} 个URL")
                for i, url in enumerate(picsum_urls):
                    print(f"      {i+1}. {url}")
            else:
                print("    ❌ Lorem Picsum获取失败")
            
            # 测试本地备用图片
            print("  🖼️ 测试本地备用图片...")
            local_urls = self.downloader._get_local_fallback_images(3)
            if local_urls:
                print(f"    ✅ 本地备用图片成功获取 {len(local_urls)} 个URL")
                for i, url in enumerate(local_urls):
                    print(f"      {i+1}. {url}")
            else:
                print("    ❌ 本地备用图片获取失败")
            
            # 测试综合API方法
            print("  🔄 测试综合API方法...")
            api_urls = self.downloader._get_alternative_image_apis(2, "nature")
            if api_urls:
                print(f"    ✅ 综合API成功获取 {len(api_urls)} 个URL")
                for i, url in enumerate(api_urls):
                    print(f"      {i+1}. {url}")
                return True
            else:
                print("    ❌ 综合API获取失败")
                return False
                
        except Exception as e:
            print(f"  ❌ 备用API测试失败: {e}")
            return False
    
    def test_url_extraction_methods(self):
        """测试URL提取方法"""
        print("\n🔍 测试URL提取方法...")
        
        if not self.downloader:
            print("  ❌ 下载器未初始化")
            return False
        
        # 检查新增的方法
        methods_to_check = [
            '_extract_image_urls_from_page',
            '_extract_urls_from_dom',
            '_can_scroll_to_element',
            '_get_alternative_image_apis'
        ]
        
        missing_methods = []
        for method_name in methods_to_check:
            if not hasattr(self.downloader, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"  ❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("  ✅ 所有新增方法都存在")
            return True
    
    def test_image_download_with_fallback(self):
        """测试带备用方案的图片下载"""
        print("\n📥 测试带备用方案的图片下载...")
        
        if not self.downloader:
            print("  ❌ 下载器未初始化")
            return False
        
        # 创建测试目录
        test_dir = "./test_downloads"
        os.makedirs(test_dir, exist_ok=True)
        
        try:
            # 测试搜索和下载
            test_queries = ["nature", "technology", "abstract"]
            
            for query in test_queries:
                print(f"  🔍 测试搜索: {query}")
                
                try:
                    # 使用备用方法下载
                    downloaded_images = self.downloader._download_fallback_images(
                        test_dir, limit=1
                    )
                    
                    if downloaded_images:
                        print(f"    ✅ 成功下载 {len(downloaded_images)} 张图片")
                        for img_path in downloaded_images:
                            if os.path.exists(img_path):
                                file_size = os.path.getsize(img_path)
                                print(f"      📁 {os.path.basename(img_path)} ({file_size} 字节)")
                            else:
                                print(f"      ❌ 文件不存在: {img_path}")
                    else:
                        print(f"    ⚠️  {query} 搜索未下载到图片")
                        
                except Exception as e:
                    print(f"    ❌ {query} 搜索失败: {e}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 图片下载测试失败: {e}")
            return False
        finally:
            # 清理测试文件
            try:
                import shutil
                if os.path.exists(test_dir):
                    shutil.rmtree(test_dir)
                    print("  🧹 清理测试文件完成")
            except Exception as e:
                print(f"  ⚠️  清理测试文件失败: {e}")
    
    def test_connection_and_setup(self):
        """测试连接和设置"""
        print("\n🔗 测试连接和设置...")
        
        try:
            self.downloader = ImageDownloader(self.config_manager)
            
            # 测试连接
            success, message = self.downloader.test_connection()
            if success:
                print(f"  ✅ 连接测试成功: {message}")
            else:
                print(f"  ⚠️  连接测试失败: {message}")
            
            # 检查配置
            min_width, min_height = self.downloader._get_minimum_size()
            print(f"  📏 最小图片尺寸: {min_width}x{min_height}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 连接和设置测试失败: {e}")
            return False
    
    def test_url_validation(self):
        """测试URL验证"""
        print("\n✅ 测试URL验证...")
        
        if not self.downloader:
            print("  ❌ 下载器未初始化")
            return False
        
        # 测试URL
        test_urls = [
            "https://picsum.photos/800/600",
            "https://via.placeholder.com/800x600",
            "https://fonts.gstatic.com/s/i/productlogos/googleg/v6/24px.svg",  # 应该被过滤
            "invalid-url",  # 无效URL
            "https://example.com/image.jpg"
        ]
        
        valid_count = 0
        for url in test_urls:
            try:
                # 简单的URL验证
                if url.startswith("http") and any(ext in url.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
                    if "gstatic" not in url.lower():
                        valid_count += 1
                        print(f"  ✅ 有效URL: {url}")
                    else:
                        print(f"  🚫 过滤URL: {url}")
                else:
                    print(f"  ❌ 无效URL: {url}")
            except Exception as e:
                print(f"  ❌ URL验证异常: {url} - {e}")
        
        print(f"  📊 有效URL数量: {valid_count}/{len(test_urls)}")
        return valid_count > 0
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始图片下载改进测试")
        print("=" * 60)
        
        tests = [
            ("连接和设置", self.test_connection_and_setup),
            ("URL提取方法", self.test_url_extraction_methods),
            ("备用API", self.test_alternative_apis),
            ("URL验证", self.test_url_validation),
            ("图片下载", self.test_image_download_with_fallback),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 {test_name}测试")
                if test_func():
                    passed_tests += 1
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 异常: {e}")
                logger.error(f"测试 {test_name} 时发生异常", exc_info=True)
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            print("🎉 所有图片下载改进测试通过！")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关功能。")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.downloader:
            try:
                self.downloader.cleanup()
            except Exception as e:
                logger.warning(f"清理下载器资源失败: {e}")

def main():
    """主函数"""
    tester = ImageDownloadTester()
    
    try:
        success = tester.run_all_tests()
        
        print("\n📋 改进总结:")
        print("1. ✅ 新增多个备用图片API源")
        print("2. ✅ 改进Google图片选择器，支持2024年新结构")
        print("3. ✅ 增加页面源代码和DOM提取方法")
        print("4. ✅ 完善错误处理和备用方案")
        print("5. ✅ 提供稳定的本地备用图片")
        
        print("\n💡 使用建议:")
        print("- 如果Google搜索失败，系统会自动使用备用API")
        print("- Lorem Picsum提供稳定的随机图片")
        print("- 本地备用图片确保总能获取到图片")
        print("- 所有方法都有详细的日志记录")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.error("测试失败", exc_info=True)
        return 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
