#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片下载优化测试脚本
验证大尺寸图片下载功能
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.image_downloader import ImageDownloader
from config import ConfigManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ImageDownloadTest")

def test_image_download():
    """测试图片下载功能"""
    
    print("🔍 开始测试图片下载优化效果...")
    
    # 初始化配置和下载器
    config_manager = ConfigManager()
    downloader = ImageDownloader(config_manager)
    
    # 测试关键词列表
    test_keywords = [
        "beautiful landscape",
        "modern architecture", 
        "natural scenery",
        "city skyline",
        "mountain view"
    ]
    
    # 创建测试目录
    test_dir = "./test_images"
    os.makedirs(test_dir, exist_ok=True)
    
    results = []
    
    for i, keyword in enumerate(test_keywords):
        print(f"\n📸 测试 {i+1}/{len(test_keywords)}: {keyword}")
        
        # 为每个关键词创建子目录
        keyword_dir = os.path.join(test_dir, f"test_{i+1}_{keyword.replace(' ', '_')}")
        
        try:
            start_time = time.time()
            
            # 下载图片
            downloaded_images = downloader.search_and_download(
                keyword=keyword,
                output_dir=keyword_dir,
                limit=1,
                progress_callback=lambda current, total, status: print(f"  进度: {status}")
            )
            
            download_time = time.time() - start_time
            
            # 分析结果
            if downloaded_images:
                image_path = downloaded_images[0]
                
                # 获取图片信息
                try:
                    from PIL import Image
                    img = Image.open(image_path)
                    width, height = img.size
                    file_size = os.path.getsize(image_path)
                    
                    result = {
                        "keyword": keyword,
                        "success": True,
                        "image_path": image_path,
                        "width": width,
                        "height": height,
                        "file_size": file_size,
                        "download_time": download_time,
                        "meets_requirement": width >= 1024 and height >= 768
                    }
                    
                    print(f"  ✅ 成功: {width}x{height}, {file_size//1024}KB, {download_time:.1f}s")
                    
                    if result["meets_requirement"]:
                        print(f"  🎯 尺寸符合要求 (≥1024x768)")
                    else:
                        print(f"  ⚠️  尺寸不符合要求 (<1024x768)")
                        
                except Exception as e:
                    result = {
                        "keyword": keyword,
                        "success": False,
                        "error": f"图片分析失败: {e}",
                        "download_time": download_time
                    }
                    print(f"  ❌ 图片分析失败: {e}")
            else:
                result = {
                    "keyword": keyword,
                    "success": False,
                    "error": "未下载到图片",
                    "download_time": download_time
                }
                print(f"  ❌ 未下载到图片")
                
            results.append(result)
            
        except Exception as e:
            result = {
                "keyword": keyword,
                "success": False,
                "error": str(e),
                "download_time": 0
            }
            results.append(result)
            print(f"  ❌ 下载失败: {e}")
    
    # 生成测试报告
    generate_test_report(results)
    
    return results

def generate_test_report(results):
    """生成测试报告"""
    
    print("\n" + "="*60)
    print("📊 图片下载测试报告")
    print("="*60)
    
    # 统计信息
    total_tests = len(results)
    successful_downloads = sum(1 for r in results if r["success"])
    meets_requirement = sum(1 for r in results if r.get("meets_requirement", False))
    
    print(f"\n📈 总体统计:")
    print(f"  测试总数: {total_tests}")
    print(f"  成功下载: {successful_downloads} ({successful_downloads/total_tests*100:.1f}%)")
    print(f"  符合尺寸要求: {meets_requirement} ({meets_requirement/total_tests*100:.1f}%)")
    
    # 详细结果
    print(f"\n📋 详细结果:")
    for i, result in enumerate(results, 1):
        print(f"\n  {i}. {result['keyword']}")
        if result["success"]:
            width = result.get("width", 0)
            height = result.get("height", 0)
            file_size = result.get("file_size", 0)
            download_time = result.get("download_time", 0)
            
            print(f"     状态: ✅ 成功")
            print(f"     尺寸: {width}x{height}")
            print(f"     大小: {file_size//1024}KB")
            print(f"     耗时: {download_time:.1f}s")
            print(f"     符合要求: {'是' if result.get('meets_requirement', False) else '否'}")
        else:
            print(f"     状态: ❌ 失败")
            print(f"     错误: {result.get('error', '未知错误')}")
    
    # 性能分析
    successful_results = [r for r in results if r["success"]]
    if successful_results:
        avg_width = sum(r.get("width", 0) for r in successful_results) / len(successful_results)
        avg_height = sum(r.get("height", 0) for r in successful_results) / len(successful_results)
        avg_size = sum(r.get("file_size", 0) for r in successful_results) / len(successful_results)
        avg_time = sum(r.get("download_time", 0) for r in successful_results) / len(successful_results)
        
        print(f"\n📊 性能分析:")
        print(f"  平均尺寸: {avg_width:.0f}x{avg_height:.0f}")
        print(f"  平均大小: {avg_size//1024:.0f}KB")
        print(f"  平均耗时: {avg_time:.1f}s")
    
    # 建议
    print(f"\n💡 优化建议:")
    
    if meets_requirement < total_tests:
        print(f"  - 有 {total_tests - meets_requirement} 张图片尺寸不符合要求")
        print(f"  - 建议检查配置中的尺寸设置")
        print(f"  - 可以尝试调整搜索关键词")
    
    if successful_downloads < total_tests:
        print(f"  - 有 {total_tests - successful_downloads} 次下载失败")
        print(f"  - 建议检查网络连接和Chrome驱动")
        print(f"  - 可以尝试使用代理服务器")
    
    if successful_downloads == total_tests and meets_requirement == total_tests:
        print(f"  - 🎉 所有测试都成功且符合要求！")
        print(f"  - 图片下载优化效果良好")
    
    # 保存报告到文件
    report_file = "./test_images/download_test_report.txt"
    try:
        with open(report_file, "w", encoding="utf-8") as f:
            f.write("图片下载测试报告\n")
            f.write("="*40 + "\n\n")
            f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试总数: {total_tests}\n")
            f.write(f"成功下载: {successful_downloads}\n")
            f.write(f"符合尺寸要求: {meets_requirement}\n\n")
            
            for i, result in enumerate(results, 1):
                f.write(f"{i}. {result['keyword']}\n")
                if result["success"]:
                    f.write(f"   尺寸: {result.get('width', 0)}x{result.get('height', 0)}\n")
                    f.write(f"   大小: {result.get('file_size', 0)//1024}KB\n")
                    f.write(f"   符合要求: {'是' if result.get('meets_requirement', False) else '否'}\n")
                else:
                    f.write(f"   失败: {result.get('error', '未知错误')}\n")
                f.write("\n")
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"\n⚠️  保存报告失败: {e}")

def test_configuration():
    """测试配置设置"""
    
    print("\n🔧 测试配置设置...")
    
    config_manager = ConfigManager()
    
    # 检查图片配置
    image_config = config_manager.get("general_settings.image", {})
    
    print(f"  图片目录: {image_config.get('image_dir', '未设置')}")
    print(f"  Chrome驱动: {image_config.get('chrome_driver_path', '未设置')}")
    print(f"  尺寸要求: {image_config.get('size', '未设置')}")
    print(f"  下载限制: {image_config.get('limit', '未设置')}")
    print(f"  代理设置: {image_config.get('proxy', '未设置') or '无'}")
    
    # 检查Chrome驱动是否存在
    driver_path = image_config.get('chrome_driver_path', '')
    if driver_path and os.path.exists(driver_path):
        print(f"  ✅ Chrome驱动文件存在")
    else:
        print(f"  ⚠️  Chrome驱动文件不存在或路径错误")

def main():
    """主函数"""
    
    print("🚀 图片下载优化测试工具")
    print("="*50)
    
    try:
        # 测试配置
        test_configuration()
        
        # 测试下载
        results = test_image_download()
        
        print(f"\n✨ 测试完成！")
        
        # 返回成功率
        successful_count = sum(1 for r in results if r["success"])
        return successful_count == len(results)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.error(f"测试失败: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
