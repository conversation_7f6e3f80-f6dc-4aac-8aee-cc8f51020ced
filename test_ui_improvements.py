#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI改进功能测试脚本
测试行高增大、图片栏宽度增大、缩略图显示和双击查看原图功能
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer
from PIL import Image

from config import ConfigManager
from ui.main_window import MainWindow

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("UIImprovementTest")

class UIImprovementTester:
    """UI改进功能测试器"""
    
    def __init__(self):
        self.app = QApplication.instance() or QApplication(sys.argv)
        self.config_manager = ConfigManager()
        self.main_window = None
        
    def create_test_images(self):
        """创建测试图片"""
        test_dir = "./test_images"
        os.makedirs(test_dir, exist_ok=True)
        
        test_images = []
        
        # 创建几张不同尺寸的测试图片
        sizes = [(1920, 1080), (1600, 900), (1280, 720)]
        colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]
        
        for i, (size, color) in enumerate(zip(sizes, colors)):
            # 创建纯色图片
            img = Image.new('RGB', size, color)
            
            # 添加一些文字标识
            try:
                from PIL import ImageDraw, ImageFont
                draw = ImageDraw.Draw(img)
                
                # 尝试使用默认字体
                try:
                    font = ImageFont.truetype("arial.ttf", 60)
                except:
                    font = ImageFont.load_default()
                
                text = f"Test Image {i+1}\n{size[0]}x{size[1]}"
                
                # 计算文字位置（居中）
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                x = (size[0] - text_width) // 2
                y = (size[1] - text_height) // 2
                
                # 绘制文字
                draw.text((x, y), text, fill=(255, 255, 255), font=font)
                
            except Exception as e:
                logger.warning(f"添加文字失败: {e}")
            
            # 保存图片
            image_path = os.path.join(test_dir, f"test_image_{i+1}.jpg")
            img.save(image_path, "JPEG", quality=95)
            test_images.append(image_path)
            
            logger.info(f"创建测试图片: {image_path} ({size[0]}x{size[1]})")
        
        return test_images
    
    def create_test_shots(self, image_paths):
        """创建测试分镜数据"""
        test_shots = []
        
        for i, image_path in enumerate(image_paths):
            shot = {
                "id": i + 1,
                "text": f"这是第{i+1}个测试分镜的文案内容，用于测试行高和图片显示效果。",
                "prompt": f"测试分镜{i+1}的提示词",
                "en_prompt": f"Test shot {i+1} prompt",
                "tags": f"测试,分镜,第{i+1}个",
                "en_tags": f"test,shot,{i+1}",
                "image_path": image_path
            }
            test_shots.append(shot)
        
        return test_shots
    
    def test_row_height_and_column_width(self):
        """测试行高和列宽"""
        print("🔍 测试行高和列宽...")
        
        # 创建主窗口
        self.main_window = MainWindow(self.config_manager)
        self.main_window.show()
        
        # 检查行高
        table = self.main_window.shot_list.table
        row_height = table.verticalHeader().defaultSectionSize()
        
        if row_height >= 100:
            print(f"  ✅ 行高已增大: {row_height}像素")
        else:
            print(f"  ❌ 行高未增大: {row_height}像素 (应该≥100)")
            return False
        
        # 检查图片列宽
        image_column = self.main_window.shot_list.IMAGE_COLUMN
        column_width = table.columnWidth(image_column)
        
        if column_width >= 180:
            print(f"  ✅ 图片列宽已增大: {column_width}像素")
        else:
            print(f"  ❌ 图片列宽未增大: {column_width}像素 (应该≥180)")
            return False
        
        return True
    
    def test_thumbnail_display(self):
        """测试缩略图显示"""
        print("\n🖼️ 测试缩略图显示...")
        
        if not self.main_window:
            print("  ❌ 主窗口未初始化")
            return False
        
        # 创建测试图片
        image_paths = self.create_test_images()
        
        # 创建测试分镜
        test_shots = self.create_test_shots(image_paths)
        
        # 设置分镜数据
        self.main_window.shot_list.set_shots(test_shots)
        
        # 检查图片是否正确显示
        table = self.main_window.shot_list.table
        image_column = self.main_window.shot_list.IMAGE_COLUMN
        
        success_count = 0
        for row in range(len(test_shots)):
            # 获取图片小部件
            image_widget = table.cellWidget(row, image_column)
            
            if image_widget:
                # 检查是否是ClickableImageLabel
                from ui.widgets.shot_list import ClickableImageLabel
                if isinstance(image_widget, ClickableImageLabel):
                    print(f"  ✅ 第{row+1}行: 缩略图正确显示")
                    success_count += 1
                else:
                    print(f"  ❌ 第{row+1}行: 图片小部件类型错误")
            else:
                print(f"  ❌ 第{row+1}行: 没有图片小部件")
        
        if success_count == len(test_shots):
            print(f"  🎉 所有缩略图都正确显示 ({success_count}/{len(test_shots)})")
            return True
        else:
            print(f"  ⚠️  部分缩略图显示失败 ({success_count}/{len(test_shots)})")
            return False
    
    def test_double_click_functionality(self):
        """测试双击查看原图功能"""
        print("\n🖱️ 测试双击查看原图功能...")
        
        if not self.main_window:
            print("  ❌ 主窗口未初始化")
            return False
        
        # 检查信号连接
        table = self.main_window.shot_list.table
        image_column = self.main_window.shot_list.IMAGE_COLUMN
        
        # 获取第一个图片小部件
        if table.rowCount() > 0:
            image_widget = table.cellWidget(0, image_column)
            
            if image_widget:
                from ui.widgets.shot_list import ClickableImageLabel
                if isinstance(image_widget, ClickableImageLabel):
                    # 检查是否有doubleClicked信号
                    if hasattr(image_widget, 'doubleClicked'):
                        print("  ✅ 图片小部件支持双击信号")
                        
                        # 检查信号是否连接
                        if image_widget.doubleClicked.receivers() > 0:
                            print("  ✅ 双击信号已连接")
                            return True
                        else:
                            print("  ❌ 双击信号未连接")
                            return False
                    else:
                        print("  ❌ 图片小部件不支持双击信号")
                        return False
                else:
                    print("  ❌ 图片小部件类型错误")
                    return False
            else:
                print("  ❌ 没有图片小部件")
                return False
        else:
            print("  ❌ 没有分镜数据")
            return False
    
    def test_image_viewer_dialog(self):
        """测试图片查看器对话框"""
        print("\n🔍 测试图片查看器对话框...")
        
        try:
            from ui.widgets.shot_list import ImageViewerDialog
            
            # 使用第一张测试图片
            test_images = self.create_test_images()
            if test_images:
                image_path = test_images[0]
                
                # 创建图片查看器（不显示）
                viewer = ImageViewerDialog(image_path)
                
                # 检查窗口标题
                if "图片查看器" in viewer.windowTitle():
                    print("  ✅ 图片查看器窗口标题正确")
                else:
                    print("  ❌ 图片查看器窗口标题错误")
                    return False
                
                # 检查图片是否加载
                if hasattr(viewer, 'image_label') and viewer.image_label.pixmap():
                    print("  ✅ 图片查看器成功加载图片")
                else:
                    print("  ❌ 图片查看器未能加载图片")
                    return False
                
                # 关闭查看器
                viewer.close()
                return True
            else:
                print("  ❌ 没有测试图片")
                return False
                
        except Exception as e:
            print(f"  ❌ 图片查看器测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始UI改进功能测试")
        print("=" * 50)
        
        tests = [
            ("行高和列宽", self.test_row_height_and_column_width),
            ("缩略图显示", self.test_thumbnail_display),
            ("双击功能", self.test_double_click_functionality),
            ("图片查看器", self.test_image_viewer_dialog),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 异常: {e}")
                logger.error(f"测试 {test_name} 时发生异常", exc_info=True)
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！UI改进功能正常。")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关功能。")
            return False
    
    def show_demo(self):
        """显示演示"""
        if self.main_window:
            print("\n🎬 显示UI改进演示...")
            print("   - 您可以看到增大的行高和图片列宽")
            print("   - 双击任意图片可查看原图")
            print("   - 演示将持续10秒钟")
            
            # 设置定时器自动关闭
            timer = QTimer()
            timer.timeout.connect(self.main_window.close)
            timer.start(10000)  # 10秒后关闭
            
            # 运行事件循环
            self.app.exec()
    
    def cleanup(self):
        """清理资源"""
        if self.main_window:
            self.main_window.close()
        
        # 清理测试图片
        try:
            import shutil
            test_dir = "./test_images"
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
                print("🧹 清理测试图片完成")
        except Exception as e:
            logger.warning(f"清理测试图片失败: {e}")

def main():
    """主函数"""
    tester = UIImprovementTester()
    
    try:
        success = tester.run_all_tests()
        
        # 如果所有测试通过，显示演示
        if success:
            tester.show_demo()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.error("测试失败", exc_info=True)
        return 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
