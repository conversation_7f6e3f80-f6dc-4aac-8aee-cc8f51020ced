#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI模型接口模块
负责与各种AI服务（如Gemini、GPT等）进行交互，提供统一的接口。
基于最新的Gemini API进行实现，支持Gemini 2.5、2.0、1.5和1.0系列模型。
"""

import json
import logging
import time
import re
import copy
import base64
from typing import Dict, List, Any, Optional, Tuple, Callable
import threading
import queue

# 导入自定义工具
from modules.utils import retry_with_exponential_backoff

# Gemini API - 使用新的google-genai库
from google import genai
from google.genai import types
from google.api_core.exceptions import GoogleAPIError, InvalidArgument, ResourceExhausted

# OpenAI API (用于GPT和Deepseek等兼容OpenAI接口的模型)
import requests
from requests.exceptions import RequestException

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("AIModel")

class AIModelError(Exception):
    """AI模型错误类"""
    pass

class AIModel:
    """
    AI模型类，提供与AI服务交互的统一接口
    支持Gemini、OpenAI兼容模型等

    优化特性：
    - 连接池管理
    - 智能重试机制
    - 内存优化
    - 错误恢复
    """

    def __init__(self, config_manager=None):
        """
        初始化AI模型

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self._gemini_client = None
        self._current_model_config = {}
        self._connection_pool = {}  # 连接池
        self._last_request_time = 0  # 用于速率限制
        self._request_count = 0  # 请求计数

        # 性能监控
        self._performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0
        }

        # 如果提供了配置管理器，则加载当前模型配置
        if config_manager:
            self._current_model_config = config_manager.get_ai_model()

        # 初始化模型
        self._initialize_model()

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计数据
        """
        return self._performance_stats.copy()

    def reset_performance_stats(self):
        """重置性能统计"""
        self._performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0
        }

    def _update_performance_stats(self, success: bool, response_time: float):
        """
        更新性能统计

        Args:
            success: 请求是否成功
            response_time: 响应时间（秒）
        """
        self._performance_stats['total_requests'] += 1
        if success:
            self._performance_stats['successful_requests'] += 1
        else:
            self._performance_stats['failed_requests'] += 1

        # 更新平均响应时间
        total = self._performance_stats['total_requests']
        current_avg = self._performance_stats['average_response_time']
        self._performance_stats['average_response_time'] = (
            (current_avg * (total - 1) + response_time) / total
        )

    def _initialize_model(self):
        """初始化AI模型"""
        if not self._current_model_config:
            logger.warning("没有配置AI模型")
            return

        model_type = self._current_model_config.get("api_type", "").lower()

        if model_type == "gemini":
            self._initialize_gemini()
        # 其他模型类型的初始化可以在这里添加

    def _initialize_gemini(self):
        """初始化Gemini模型"""
        api_key = self._current_model_config.get("api_key", "")

        if not api_key:
            logger.warning("Gemini API密钥未配置")
            return

        try:
            # 创建Gemini客户端
            self._gemini_client = genai.Client(api_key=api_key)

            logger.info(f"Gemini模型初始化成功: {self._current_model_config.get('name')}")
        except Exception as e:
            logger.error(f"Gemini模型初始化失败: {e}")
            self._gemini_client = None

    def change_model(self, model_name: str) -> bool:
        """
        切换AI模型

        Args:
            model_name: 模型名称

        Returns:
            bool: 是否切换成功
        """
        if not self.config_manager:
            logger.error("未提供配置管理器，无法切换模型")
            return False

        # 获取模型配置
        model_config = self.config_manager.get_ai_model(model_name)

        if not model_config:
            logger.error(f"找不到模型配置: {model_name}")
            return False

        # 更新当前模型配置
        self._current_model_config = model_config
        self.config_manager.set("current_model", model_name)
        self.config_manager.save_config()

        # 重新初始化模型
        self._initialize_model()

        return True

    def cleanup_resources(self):
        """
        清理资源，释放连接
        """
        try:
            # 清理Gemini客户端
            if self._gemini_client:
                # Gemini客户端通常不需要显式关闭，但我们可以清空引用
                self._gemini_client = None
                logger.info("Gemini客户端资源已清理")

            # 清理连接池
            self._connection_pool.clear()

            # 重置计数器
            self._request_count = 0
            self._last_request_time = 0

            logger.info("AI模型资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        try:
            self.cleanup_resources()
        except:
            pass  # 忽略析构函数中的错误

    def test_connection(self) -> Tuple[bool, str]:
        """
        测试与AI模型的连接

        Returns:
            Tuple[bool, str]: (是否连接成功, 信息)
        """
        if not self._current_model_config:
            return False, "未配置AI模型"

        model_type = self._current_model_config.get("api_type", "").lower()

        if model_type == "gemini":
            return self._test_gemini_connection()
        elif model_type == "openai":
            return self._test_openai_connection()
        else:
            return False, f"不支持的模型类型: {model_type}"

    def _test_gemini_connection(self) -> Tuple[bool, str]:
        """
        测试与Gemini模型的连接

        Returns:
            Tuple[bool, str]: (是否连接成功, 信息)
        """
        if not self._gemini_client:
            return False, "Gemini模型未初始化"

        try:
            # 使用简单的提示测试连接
            model_name = self._current_model_config.get("model_name", "gemini-2.0-flash")

            # 创建生成配置
            config = types.GenerateContentConfig(
                temperature=0.1,
                max_output_tokens=10,
                top_p=1.0,
                top_k=1
            )

            # 发送简单请求
            response = self._gemini_client.models.generate_content(
                model=model_name,
                contents=["Hello"],
                config=config
            )

            # 检查响应是否有效
            if response and hasattr(response, 'text'):
                return True, "连接成功"
            else:
                return True, "连接成功（响应格式异常）"
        except GoogleAPIError as e:
            return False, f"API错误: {e}"
        except InvalidArgument as e:
            return False, f"参数错误: {e}"
        except Exception as e:
            return False, f"未知错误: {e}"

    def _test_openai_connection(self) -> Tuple[bool, str]:
        """
        测试与OpenAI兼容模型的连接

        Returns:
            Tuple[bool, str]: (是否连接成功, 信息)
        """
        api_url = self._current_model_config.get("api_url", "")
        api_key = self._current_model_config.get("api_key", "")
        model_name = self._current_model_config.get("model_name", "")

        if not api_url or not api_key or not model_name:
            return False, "API配置不完整"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        data = {
            "model": model_name,
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10
        }

        try:
            response = requests.post(api_url, headers=headers, json=data, timeout=10)
            response.raise_for_status()

            return True, "连接成功"
        except RequestException as e:
            return False, f"请求错误: {e}"
        except Exception as e:
            return False, f"未知错误: {e}"

    def generate_content(self, prompt: str,
                        stream: bool = False,
                        callback: Callable = None,
                        system_instruction: Optional[str] = None,
                        multimodal_data: Optional[List[Dict]] = None) -> Optional[str]:
        """
        生成内容

        Args:
            prompt: 提示文本
            stream: 是否使用流式输出
            callback: 回调函数，用于流式输出
            system_instruction: 系统指令
            multimodal_data: 多模态数据，如图片等

        Returns:
            Optional[str]: 生成的内容，如果生成失败则返回None
        """
        if not self._current_model_config:
            logger.error("未配置AI模型")
            raise AIModelError("未配置AI模型")

        model_type = self._current_model_config.get("api_type", "").lower()
        logger.info(f"生成内容，使用模型类型: {model_type}")
        logger.info(f"提示词长度: {len(prompt)}字符")
        logger.info(f"是否流式输出: {stream}")
        logger.info(f"是否有系统指令: {system_instruction is not None}")
        logger.info(f"是否有多模态数据: {multimodal_data is not None}")

        try:
            if model_type == "gemini":
                return self._gemini_generate(prompt, stream, callback, system_instruction, multimodal_data)
            elif model_type == "openai":
                return self._openai_generate(prompt, stream, callback, system_instruction)
            else:
                logger.error(f"不支持的模型类型: {model_type}")
                raise AIModelError(f"不支持的模型类型: {model_type}")
        except Exception as e:
            logger.error(f"生成内容时发生异常: {e}", exc_info=True)
            raise

    def generate_image(self, prompt: str, system_instruction: Optional[str] = None) -> Optional[str]:
        """
        生成图片

        Args:
            prompt: 图片描述
            system_instruction: 系统指令

        Returns:
            Optional[str]: 生成的图片URL或base64编码，如果生成失败则返回None
        """
        if not self._current_model_config:
            logger.error("未配置AI模型")
            raise AIModelError("未配置AI模型")

        model_type = self._current_model_config.get("api_type", "").lower()
        logger.info(f"生成图片，使用模型类型: {model_type}")
        logger.info(f"图片描述长度: {len(prompt)}字符")
        logger.info(f"是否有系统指令: {system_instruction is not None}")

        try:
            if model_type == "gemini":
                # 构建提示词
                image_prompt = f"""请根据以下描述生成一张图片：

{prompt}

请直接生成图片，不需要任何文字说明。
"""
                # 调用Gemini模型生成图片
                response = self._gemini_generate(image_prompt, False, None, system_instruction)

                # 从响应中提取图片URL或base64编码
                if response:
                    # 尝试从响应中提取图片URL
                    url_match = re.search(r'https?://\S+\.(jpg|jpeg|png|gif)', response, re.IGNORECASE)
                    if url_match:
                        return url_match.group(0)

                    # 尝试从响应中提取base64编码
                    base64_match = re.search(r'data:image/\w+;base64,[A-Za-z0-9+/=]+', response)
                    if base64_match:
                        return base64_match.group(0)

                    # 如果没有找到图片URL或base64编码，返回整个响应
                    return response

                return None
            else:
                logger.error(f"不支持的模型类型: {model_type}")
                raise AIModelError(f"不支持的模型类型: {model_type}，图片生成仅支持Gemini模型")
        except Exception as e:
            logger.error(f"生成图片时发生异常: {e}", exc_info=True)
            raise

    def _gemini_generate(self, prompt: str,
                         stream: bool = False,
                         callback: Callable = None,
                         system_instruction: Optional[str] = None,
                         multimodal_data: Optional[List[Dict]] = None) -> Optional[str]:
        """
        使用Gemini模型生成内容

        Args:
            prompt: 提示文本
            stream: 是否使用流式输出
            callback: 回调函数，用于流式输出
            system_instruction: 系统指令
            multimodal_data: 多模态数据，如图片等

        Returns:
            Optional[str]: 生成的内容，如果生成失败则返回None
        """
        if not self._gemini_client:
            logger.error("Gemini模型未初始化")
            raise AIModelError("Gemini模型未初始化")

        # 获取模型配置
        model_name = self._current_model_config.get("model_name", "gemini-2.0-flash")
        temperature = float(self._current_model_config.get("temperature", 0.7))
        max_output_tokens = int(self._current_model_config.get("max_output_tokens", 8192))
        top_p = float(self._current_model_config.get("top_p", 0.95))
        top_k = int(self._current_model_config.get("top_k", 40))

        logger.info(f"Gemini生成内容，使用模型: {model_name}")
        logger.info(f"参数: temperature={temperature}, max_output_tokens={max_output_tokens}, top_p={top_p}, top_k={top_k}")

        # 创建生成配置
        config = types.GenerateContentConfig(
            temperature=temperature,
            max_output_tokens=max_output_tokens,
            top_p=top_p,
            top_k=top_k
        )

        # 添加系统指令（如果有）
        if system_instruction:
            config.system_instruction = system_instruction
            logger.info(f"添加系统指令: {system_instruction[:50]}...")

        # 定义生成函数
        def generate_content():
            # 准备内容
            contents = []

            # 处理多模态数据
            if multimodal_data:
                logger.info(f"处理多模态数据，共{len(multimodal_data)}项")

                # 创建多模态内容
                for item in multimodal_data:
                    item_type = item.get('type', '')

                    if item_type == 'image' and 'data' in item:
                        # 处理图片数据
                        try:
                            # 创建图片部分
                            image_data = item['data']
                            image_part = types.Part(
                                inline_data=types.Blob(
                                    mime_type="image/jpeg",
                                    data=base64.b64decode(image_data)
                                )
                            )

                            # 创建文本部分（如果有）
                            text_part = None
                            if 'name' in item:
                                text_part = types.Part(text=f"图片: {item['name']}")

                            # 添加到内容中
                            if text_part:
                                contents.append([text_part, image_part])
                            else:
                                contents.append([image_part])

                            logger.info(f"添加了图片: {item.get('name', '未命名')}")
                        except Exception as e:
                            logger.error(f"处理图片数据时出错: {e}")

                    elif item_type == 'text' and 'data' in item:
                        # 处理文本数据
                        try:
                            text_content = f"文件: {item.get('name', '未命名')}\n\n{item['data']}"
                            contents.append([types.Part(text=text_content)])
                            logger.info(f"添加了文本: {item.get('name', '未命名')}")
                        except Exception as e:
                            logger.error(f"处理文本数据时出错: {e}")

                # 添加用户提示
                contents.append([types.Part(text=prompt)])
            else:
                # 只有文本提示
                contents = [prompt]

            # 使用局部变量而不是修改参数
            use_stream = stream

            if use_stream:
                logger.info("使用流式生成")
                # 流式生成
                try:
                    response = self._gemini_client.models.generate_content_stream(
                        model=model_name,
                        contents=contents,
                        config=config
                    )

                    logger.info("流式生成请求已发送")

                    # 完整响应
                    full_response = ""
                    chunk_count = 0

                    # 处理流式响应
                    update_threshold = 200  # 每累积200个字符更新一次
                    update_interval = 30    # 或者每30个块更新一次
                    accumulated_text = ""
                    last_update_time = time.time()

                    for chunk in response:
                        chunk_count += 1
                        if hasattr(chunk, 'text') and chunk.text:
                            # 将新块添加到完整响应
                            chunk_text = chunk.text
                            full_response += chunk_text
                            accumulated_text += chunk_text

                            # 控制更新频率：满足以下任一条件时更新
                            # 1. 累积文本超过阈值
                            # 2. 已处理的块数是更新间隔的倍数
                            # 3. 距离上次更新已经过去了至少1秒
                            current_time = time.time()
                            time_condition = (current_time - last_update_time) >= 1.0

                            if (len(accumulated_text) >= update_threshold or
                                chunk_count % update_interval == 0 or
                                time_condition):

                                # 如果提供了回调函数，则调用回调函数
                                if callback and accumulated_text:
                                    try:
                                        # 确保回调不会抛出异常
                                        callback(accumulated_text, full_response)
                                        logger.debug(f"回调函数已调用，累积块长度: {len(accumulated_text)}，完整响应长度: {len(full_response)}")
                                        # 重置累积文本和更新时间
                                        accumulated_text = ""
                                        last_update_time = current_time
                                    except Exception as e:
                                        logger.error(f"调用回调函数时出错: {e}", exc_info=True)
                                        # 出错时也重置累积文本，避免重复错误
                                        accumulated_text = ""

                    # 确保最后的累积文本也被发送
                    if accumulated_text and callback:
                        try:
                            callback(accumulated_text, full_response)
                            logger.info(f"最终回调函数已调用，累积块长度: {len(accumulated_text)}，完整响应长度: {len(full_response)}")
                        except Exception as e:
                            logger.error(f"调用最终回调函数时出错: {e}", exc_info=True)

                    logger.info(f"流式生成完成，共收到{chunk_count}个块，总长度{len(full_response)}字符")
                    return full_response
                except Exception as e:
                    logger.error(f"流式生成失败: {e}，回退到非流式生成")
                    # 如果流式生成失败，回退到非流式生成
                    use_stream = False

            if not use_stream:
                logger.info("使用非流式生成")
                # 非流式生成
                response = self._gemini_client.models.generate_content(
                    model=model_name,
                    contents=contents,
                    config=config
                )

                # 返回响应文本
                if hasattr(response, 'text'):
                    logger.info(f"非流式生成完成，响应长度: {len(response.text)}字符")
                    return response.text
                else:
                    logger.info(f"非流式生成完成，但响应没有text属性，返回字符串表示")
                    return str(response)

        # 定义重试条件：对资源耗尽错误、服务器过载错误和JSON解析错误进行重试
        def retry_condition(e):
            error_str = str(e)
            return (isinstance(e, ResourceExhausted) or
                    "429" in error_str or
                    "RESOURCE_EXHAUSTED" in error_str or
                    "限速" in error_str or
                    "503" in error_str or
                    "UNAVAILABLE" in error_str or
                    "overloaded" in error_str or
                    "服务器过载" in error_str or
                    "JSONDecodeError" in error_str or
                    "Expecting property name" in error_str or
                    "json.decoder" in error_str)

        # 定义重试回调
        def on_retry(retry_count, delay, exception):
            error_str = str(exception)
            if "JSONDecodeError" in error_str or "Expecting property name" in error_str or "json.decoder" in error_str:
                logger.warning(f"遇到JSON解析错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")
            elif "503" in error_str or "UNAVAILABLE" in error_str or "overloaded" in error_str:
                logger.warning(f"遇到服务器过载错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")
            else:
                logger.warning(f"遇到API限速错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")

        try:
            # 使用通用重试函数执行生成
            return retry_with_exponential_backoff(
                func=generate_content,
                max_retries=8,  # 增加重试次数
                base_delay=3.0,  # 增加基础延迟
                retry_exceptions=(Exception,),
                retry_condition=retry_condition,
                on_retry=on_retry
            )
        except ResourceExhausted as e:
            logger.error(f"Gemini API资源耗尽错误: {e}", exc_info=True)
            raise AIModelError(f"Gemini API限速错误: {e}")
        except GoogleAPIError as e:
            error_str = str(e)
            if "503" in error_str or "UNAVAILABLE" in error_str or "overloaded" in error_str:
                logger.error(f"Gemini服务器过载错误: {e}", exc_info=True)
                raise AIModelError(f"Gemini服务器当前过载，请稍后再试。建议减小批量处理的数量或等待几分钟后重试。错误详情: {e}")
            else:
                logger.error(f"Gemini API错误: {e}", exc_info=True)
                raise AIModelError(f"Gemini API错误: {e}")
        except InvalidArgument as e:
            logger.error(f"Gemini参数错误: {e}", exc_info=True)
            raise AIModelError(f"Gemini参数错误: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"Gemini API返回的JSON解析错误: {e}", exc_info=True)
            raise AIModelError(f"Gemini API返回了无效的JSON数据，这通常是由于服务器端的临时问题。请稍后再试。错误详情: {e}")
        except Exception as e:
            error_str = str(e)
            if "JSONDecodeError" in error_str or "Expecting property name" in error_str or "json.decoder" in error_str:
                logger.error(f"Gemini API返回的JSON解析错误: {e}", exc_info=True)
                raise AIModelError(f"Gemini API返回了无效的JSON数据，这通常是由于服务器端的临时问题。请稍后再试。错误详情: {e}")
            elif "503" in error_str or "UNAVAILABLE" in error_str or "overloaded" in error_str:
                logger.error(f"Gemini服务器过载错误: {e}", exc_info=True)
                raise AIModelError(f"Gemini服务器当前过载，请稍后再试。建议减小批量处理的数量或等待几分钟后重试。错误详情: {e}")
            else:
                logger.error(f"Gemini生成内容失败: {e}", exc_info=True)
                raise AIModelError(f"Gemini生成内容失败: {e}")

    def _openai_generate(self, prompt: str,
                         stream: bool = False,
                         callback: Callable = None,
                         system_instruction: Optional[str] = None) -> Optional[str]:
        """
        使用OpenAI兼容模型生成内容

        Args:
            prompt: 提示文本
            stream: 是否使用流式输出
            callback: 回调函数，用于流式输出
            system_instruction: 系统指令

        Returns:
            Optional[str]: 生成的内容，如果生成失败则返回None
        """
        api_url = self._current_model_config.get("api_url", "")
        api_key = self._current_model_config.get("api_key", "")
        model_name = self._current_model_config.get("model_name", "")
        temperature = float(self._current_model_config.get("temperature", 0.7))
        max_tokens = int(self._current_model_config.get("max_tokens", 4000))
        top_p = float(self._current_model_config.get("top_p", 1.0))

        if not api_url or not api_key or not model_name:
            raise AIModelError("API配置不完整")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        # 准备消息
        messages = []

        # 如果提供了系统指令，则添加系统指令
        if system_instruction:
            messages.append({"role": "system", "content": system_instruction})

        # 添加用户消息
        messages.append({"role": "user", "content": prompt})

        # 准备请求数据
        data = {
            "model": model_name,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": top_p,
            "stream": stream
        }

        try:
            if stream:
                # 流式生成
                response = requests.post(api_url, headers=headers, json=data, stream=True, timeout=60)
                response.raise_for_status()

                # 完整响应
                full_response = ""

                # 处理流式响应
                for line in response.iter_lines():
                    if line:
                        # 移除"data: "前缀
                        line = line.decode('utf-8')
                        if line.startswith("data: "):
                            line = line[6:]

                            # 跳过[DONE]
                            if line == "[DONE]":
                                continue

                            try:
                                # 解析JSON
                                chunk = json.loads(line)

                                # 提取内容
                                if "choices" in chunk and len(chunk["choices"]) > 0:
                                    choice = chunk["choices"][0]

                                    if "delta" in choice and "content" in choice["delta"]:
                                        content = choice["delta"]["content"]

                                        # 将新块添加到完整响应
                                        full_response += content

                                        # 如果提供了回调函数，则调用回调函数
                                        if callback:
                                            callback(content, full_response)
                            except json.JSONDecodeError:
                                pass

                return full_response
            else:
                # 非流式生成
                response = requests.post(api_url, headers=headers, json=data, timeout=60)
                response.raise_for_status()

                # 解析响应
                response_data = response.json()

                # 提取内容
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    choice = response_data["choices"][0]

                    if "message" in choice and "content" in choice["message"]:
                        return choice["message"]["content"]

                return None
        except RequestException as e:
            logger.error(f"OpenAI请求错误: {e}")
            raise AIModelError(f"OpenAI请求错误: {e}")
        except Exception as e:
            logger.error(f"OpenAI生成内容失败: {e}")
            raise AIModelError(f"OpenAI生成内容失败: {e}")

    def batch_translate_optimized(self, items: List[Dict[str, str]],
                                progress_callback: Callable = None,
                                batch_size: int = 5) -> List[Dict[str, str]]:
        """
        优化的批量翻译方法，将多个分镜的提示词和标签合并在一起发送给AI，减少API请求次数

        Args:
            items: 要翻译的项目列表，每个项目为包含'id', 'prompt', 'tags'的字典
            progress_callback: 进度回调函数，接收参数(current, total, status)
            batch_size: 每批处理的项目数量

        Returns:
            List[Dict[str, str]]: 翻译结果列表，每个项目为包含'id', 'prompt', 'tags', 'en_prompt', 'en_tags'的字典
        """
        if not items:
            return []

        # 创建结果列表的副本，保留原始ID和其他字段
        results = copy.deepcopy(items)

        # 计算批次数量
        total_items = len(items)
        batch_count = (total_items + batch_size - 1) // batch_size  # 向上取整

        logger.info(f"开始批量翻译，共{total_items}个项目，分{batch_count}批处理，每批{batch_size}个")

        # 更新进度
        if progress_callback:
            progress_callback(0, total_items, "准备批量翻译...")

        # 按批次处理
        for batch_index in range(batch_count):
            # 计算当前批次的起始和结束索引
            start_idx = batch_index * batch_size
            end_idx = min(start_idx + batch_size, total_items)
            current_batch = items[start_idx:end_idx]

            # 更新进度
            if progress_callback:
                progress_callback(start_idx, total_items, f"处理第{batch_index+1}/{batch_count}批...")

            # 构建批量翻译提示词
            batch_prompt = self._build_batch_translation_prompt(current_batch)

            # 记录提示词长度，用于调试
            logger.info(f"批次{batch_index+1}提示词长度: {len(batch_prompt)}字符")
            logger.info(f"批次{batch_index+1}包含{len(current_batch)}个项目")

            # 定义生成函数
            def generate_batch_content():
                logger.info(f"开始批次{batch_index+1}的翻译请求")
                result = self.generate_content(batch_prompt)
                logger.info(f"批次{batch_index+1}翻译请求完成，结果长度: {len(result)}字符")
                return result

            # 定义重试条件：对资源耗尽错误、服务器过载错误和JSON解析错误进行重试
            def retry_condition(e):
                error_str = str(e)
                return isinstance(e, AIModelError) and (
                    "429" in error_str or
                    "RESOURCE_EXHAUSTED" in error_str or
                    "限速" in error_str or
                    "503" in error_str or
                    "UNAVAILABLE" in error_str or
                    "overloaded" in error_str or
                    "服务器过载" in error_str or
                    "JSONDecodeError" in error_str or
                    "Expecting property name" in error_str or
                    "json.decoder" in error_str)

            # 定义重试回调
            def on_retry(retry_count, delay, exception):
                error_str = str(exception)
                if "JSONDecodeError" in error_str or "Expecting property name" in error_str or "json.decoder" in error_str:
                    logger.warning(f"批量翻译时遇到JSON解析错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")
                    if progress_callback:
                        progress_callback(start_idx, total_items, f"JSON解析错误，等待重试({retry_count}/8)...")
                elif "503" in error_str or "UNAVAILABLE" in error_str or "overloaded" in error_str:
                    logger.warning(f"批量翻译时遇到服务器过载错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")
                    if progress_callback:
                        progress_callback(start_idx, total_items, f"服务器过载，等待重试({retry_count}/8)...")
                else:
                    logger.warning(f"批量翻译时遇到API限速错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")
                    if progress_callback:
                        progress_callback(start_idx, total_items, f"API限速，等待重试({retry_count}/8)...")

            try:
                # 使用通用重试函数执行生成
                batch_result = retry_with_exponential_backoff(
                    func=generate_batch_content,
                    max_retries=8,  # 增加重试次数
                    base_delay=3.0,  # 增加基础延迟
                    retry_exceptions=(Exception,),
                    retry_condition=retry_condition,
                    on_retry=on_retry
                )
            except Exception as e:
                # 如果重试后仍然失败，回退到单个翻译
                logger.error(f"批量翻译失败: {e}")
                if progress_callback:
                    progress_callback(end_idx, total_items, f"批量翻译失败: {e}")
                logger.info("回退到单个翻译模式")
                if progress_callback:
                    progress_callback(start_idx, total_items, "回退到单个翻译模式...")
                return self.batch_translate(items, progress_callback)

            # 解析批量翻译结果
            if batch_result:
                try:
                    logger.info(f"开始解析批次{batch_index+1}的翻译结果")
                    parsed_results = self._parse_batch_translation_result(batch_result, current_batch)

                    # 检查解析结果
                    missing_count = sum(1 for item in parsed_results if not item.get("en_prompt") or not item.get("en_tags"))
                    if missing_count > 0:
                        logger.warning(f"批次{batch_index+1}解析结果中有{missing_count}/{len(current_batch)}个项目没有得到完整翻译")

                        # 如果大部分项目都没有得到翻译，可能是解析问题，尝试单个翻译
                        if missing_count > len(current_batch) * 0.7:  # 如果超过70%的项目没有得到翻译
                            logger.warning(f"批次{batch_index+1}大部分项目({missing_count}/{len(current_batch)})没有得到翻译，尝试单个翻译")

                            # 对当前批次使用单个翻译
                            if progress_callback:
                                progress_callback(start_idx, total_items, f"批次{batch_index+1}解析问题，使用单个翻译...")

                            # 使用单个翻译处理当前批次
                            def create_progress_callback(start_idx, total_items, progress_callback):
                                if progress_callback:
                                    return lambda c, t, s: progress_callback(start_idx + c, total_items, s)
                                return None

                            single_results = self.batch_translate(current_batch,
                                                                create_progress_callback(start_idx, total_items, progress_callback))

                            # 更新结果
                            for i, item in enumerate(single_results):
                                idx = start_idx + i
                                if idx < len(results):
                                    results[idx]["en_prompt"] = item.get("en_prompt", results[idx].get("prompt", ""))
                                    results[idx]["en_tags"] = item.get("en_tags", results[idx].get("tags", ""))

                            # 继续下一个批次
                            continue

                    # 更新结果
                    for i, item in enumerate(parsed_results):
                        idx = start_idx + i
                        if idx < len(results):
                            results[idx]["en_prompt"] = item.get("en_prompt", results[idx].get("prompt", ""))
                            results[idx]["en_tags"] = item.get("en_tags", results[idx].get("tags", ""))

                    logger.info(f"批次{batch_index+1}解析完成")
                except Exception as e:
                    logger.error(f"解析批量翻译结果失败: {e}")
                    # 如果解析失败，对当前批次使用单个翻译
                    logger.info(f"批次{batch_index+1}解析失败，使用单个翻译")
                    if progress_callback:
                        progress_callback(start_idx, total_items, f"批次{batch_index+1}解析失败，使用单个翻译...")

                    # 使用单个翻译处理当前批次
                    def create_progress_callback_2(start_idx, total_items, progress_callback):
                        if progress_callback:
                            return lambda c, t, s: progress_callback(start_idx + c, total_items, s)
                        return None

                    single_results = self.batch_translate(current_batch,
                                                        create_progress_callback_2(start_idx, total_items, progress_callback))

                    # 更新结果
                    for i, item in enumerate(single_results):
                        idx = start_idx + i
                        if idx < len(results):
                            results[idx]["en_prompt"] = item.get("en_prompt", results[idx].get("prompt", ""))
                            results[idx]["en_tags"] = item.get("en_tags", results[idx].get("tags", ""))

            # 更新进度
            if progress_callback:
                progress_callback(end_idx, total_items, f"完成第{batch_index+1}/{batch_count}批")

        # 完成所有批次
        if progress_callback:
            progress_callback(total_items, total_items, "翻译完成")

        # 检查最终结果
        missing_translations = [i for i, item in enumerate(results) if not item.get("en_prompt") or not item.get("en_tags")]
        if missing_translations:
            logger.warning(f"最终结果中有{len(missing_translations)}/{total_items}个项目没有得到完整翻译: {missing_translations}")

            # 对于没有得到翻译的项目，使用原始中文内容
            for idx in missing_translations:
                if not results[idx].get("en_prompt"):
                    results[idx]["en_prompt"] = results[idx].get("prompt", "")
                if not results[idx].get("en_tags"):
                    results[idx]["en_tags"] = results[idx].get("tags", "")

        return results

    def _build_batch_translation_prompt(self, items: List[Dict[str, str]]) -> str:
        """
        构建批量翻译提示词

        Args:
            items: 要翻译的项目列表

        Returns:
            str: 批量翻译提示词
        """
        # 构建批量翻译提示词
        prompt = "你是一个专业的中英文翻译专家。请将以下中文提示词和标签翻译成英文，保持原意的同时确保翻译的准确性和自然度。\n\n"
        prompt += "重要提示：你必须为每个项目提供翻译，不要遗漏任何项目。\n\n"

        # 记录项目ID列表，用于最后的检查
        id_list = []

        for i, item in enumerate(items):
            item_id = item.get("id", i + 1)
            id_list.append(item_id)
            item_prompt = item.get("prompt", "")
            item_tags = item.get("tags", "")

            prompt += f"项目{item_id}:\n"
            prompt += f"中文提示词: {item_prompt}\n"
            prompt += f"中文标签: {item_tags}\n\n"

        prompt += "===翻译格式要求===\n"
        prompt += "请严格按以下格式输出每个项目的翻译结果:\n\n"

        # 为每个项目提供一个示例格式
        for item_id in id_list:
            prompt += f"项目{item_id}:\n"
            prompt += "英文提示词: [此处是项目" + str(item_id) + "的英文提示词]\n"
            prompt += "英文标签: [此处是项目" + str(item_id) + "的英文标签]\n\n"

        prompt += "===重要规则===\n"
        prompt += "1. 必须翻译所有项目，不要遗漏\n"
        prompt += "2. 不要添加任何额外的解释或注释\n"
        prompt += "3. 保持项目ID与输入一致\n"
        prompt += "4. 每个项目必须包含项目ID、英文提示词和英文标签三部分\n"
        prompt += f"5. 请确认你已经翻译了所有{len(items)}个项目: {', '.join(map(str, id_list))}\n"

        return prompt

    def _parse_batch_translation_result(self, result: str, original_items: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        解析批量翻译结果

        Args:
            result: 批量翻译结果
            original_items: 原始项目列表，用于获取ID

        Returns:
            List[Dict[str, Any]]: 解析后的结果列表
        """
        # 创建结果列表
        parsed_results = []

        # 初始化结果，确保即使解析失败也有默认值
        for item in original_items:
            parsed_results.append({
                "id": item.get("id", 0),
                "en_prompt": "",
                "en_tags": ""
            })

        # 记录原始结果，用于调试
        logger.info(f"原始翻译结果长度: {len(result)}")
        logger.info(f"原始翻译结果前100字符: {result[:100]}")
        logger.info(f"原始翻译结果后100字符: {result[-100:] if len(result) > 100 else result}")

        # 预处理结果文本，移除可能干扰解析的特殊字符
        result = result.replace('```', '').replace('```json', '')

        # 统一解析方法，按项目ID分块处理
        try:
            # 首先尝试使用更可靠的模式匹配所有项目块
            blocks = re.findall(r"项目\s*(?:ID\s*[:：])?\s*(\d+)[:：]?(.*?)(?=项目\s*(?:ID\s*[:：])?\s*\d+[:：]?|$)", result, re.DOTALL)

            if blocks:
                logger.info(f"找到{len(blocks)}个翻译块")

                # 创建ID到索引的映射，加速查找
                id_to_index = {item.get("id", 0): i for i, item in enumerate(original_items)}

                for block in blocks:
                    try:
                        item_id = int(block[0].strip())
                        content = block[1].strip()

                        # 使用映射快速查找索引
                        if item_id in id_to_index:
                            idx = id_to_index[item_id]

                            # 提取英文提示词和标签
                            en_prompt = ""
                            en_tags = ""

                            # 使用更健壮的正则表达式模式
                            prompt_pattern = r"(?:英文提示词|English\s+prompt)[:：]?\s*(.*?)(?=(?:英文标签|English\s+tags)[:：]?|\n项目|$)"
                            tags_pattern = r"(?:英文标签|English\s+tags)[:：]?\s*(.*?)(?=\n项目|$)"

                            prompt_match = re.search(prompt_pattern, content, re.DOTALL | re.IGNORECASE)
                            if prompt_match:
                                en_prompt = prompt_match.group(1).strip()

                            tags_match = re.search(tags_pattern, content, re.DOTALL | re.IGNORECASE)
                            if tags_match:
                                en_tags = tags_match.group(1).strip()

                            # 如果正则表达式失败，尝试按行解析
                            if not en_prompt or not en_tags:
                                lines = content.split('\n')
                                for j, line in enumerate(lines):
                                    line = line.strip()
                                    if re.search(r"(?:英文提示词|English\s+prompt)", line, re.IGNORECASE) and j+1 < len(lines):
                                        en_prompt = lines[j+1].strip()
                                    elif re.search(r"(?:英文标签|English\s+tags)", line, re.IGNORECASE) and j+1 < len(lines):
                                        en_tags = lines[j+1].strip()

                            # 更新结果
                            if en_prompt:
                                parsed_results[idx]["en_prompt"] = en_prompt
                            if en_tags:
                                parsed_results[idx]["en_tags"] = en_tags

                            logger.info(f"项目{item_id}解析结果: 提示词长度={len(en_prompt)}, 标签长度={len(en_tags)}")
                        else:
                            logger.warning(f"找不到匹配的原始项目ID: {item_id}")
                    except Exception as e:
                        logger.error(f"解析项目块失败: {e}")
            else:
                # 如果按项目ID分块失败，尝试直接提取所有英文提示词和标签
                logger.info("按项目ID分块失败，尝试直接提取所有英文提示词和标签")

                # 提取所有英文提示词和标签
                en_prompts = re.findall(r"(?:英文提示词|English\s+prompt)[:：]?\s*(.*?)(?=(?:英文标签|English\s+tags)[:：]?|\n项目|$)", result, re.DOTALL | re.IGNORECASE)
                en_tags_list = re.findall(r"(?:英文标签|English\s+tags)[:：]?\s*(.*?)(?=\n项目|$)", result, re.DOTALL | re.IGNORECASE)

                logger.info(f"找到{len(en_prompts)}个提示词和{len(en_tags_list)}个标签")

                # 如果数量匹配，按顺序分配
                if len(en_prompts) == len(original_items) and len(en_tags_list) == len(original_items):
                    for i, (prompt, tag) in enumerate(zip(en_prompts, en_tags_list)):
                        parsed_results[i]["en_prompt"] = prompt.strip()
                        parsed_results[i]["en_tags"] = tag.strip()

                    logger.info("成功匹配所有项目")
                elif len(en_prompts) > 0 and len(en_tags_list) > 0:
                    # 如果数量不匹配但至少有一些内容，尝试按顺序分配尽可能多的内容
                    min_count = min(len(en_prompts), len(en_tags_list), len(original_items))
                    for i in range(min_count):
                        parsed_results[i]["en_prompt"] = en_prompts[i].strip()
                        parsed_results[i]["en_tags"] = en_tags_list[i].strip()

                    logger.info(f"部分匹配: 匹配了{min_count}个项目")
        except Exception as e:
            logger.error(f"解析批量翻译结果失败: {e}")

        # 检查是否有任何项目没有得到翻译
        missing_translations = [i for i, item in enumerate(parsed_results) if not item["en_prompt"] or not item["en_tags"]]
        if missing_translations:
            logger.warning(f"有{len(missing_translations)}个项目没有得到完整翻译: {missing_translations}")

            # 对于没有得到翻译的项目，使用原始中文内容
            for idx in missing_translations:
                if idx < len(original_items):
                    if not parsed_results[idx]["en_prompt"]:
                        parsed_results[idx]["en_prompt"] = original_items[idx].get("prompt", "")
                        logger.info(f"项目{parsed_results[idx]['id']}使用原始中文提示词作为英文提示词")
                    if not parsed_results[idx]["en_tags"]:
                        parsed_results[idx]["en_tags"] = original_items[idx].get("tags", "")
                        logger.info(f"项目{parsed_results[idx]['id']}使用原始中文标签作为英文标签")

        return parsed_results

    def batch_translate(self, items: List[Dict[str, str]],
                        progress_callback: Callable = None,
                        max_threads: int = 4) -> List[Dict[str, str]]:
        """
        批量翻译

        Args:
            items: 要翻译的项目列表，每个项目为包含'id', 'prompt', 'tags'的字典
            progress_callback: 进度回调函数，接收参数(current, total)
            max_threads: 最大线程数

        Returns:
            List[Dict[str, str]]: 翻译结果列表，每个项目为包含'id', 'prompt', 'tags', 'en_prompt', 'en_tags'的字典
        """
        if not items:
            return []

        # 加载翻译提示词
        translation_prompt_template = ""
        if self.config_manager:
            translation_prompt_template = self.config_manager.get("prompt_settings.translation_prompt", "")

        if not translation_prompt_template:
            translation_prompt_template = "请将以下中文提示词和标签翻译成英文，保持原意的同时确保翻译的准确性和自然度。\n\n中文提示词：{prompt}\n标签：{tags}\n\n请按以下格式输出：\n英文提示词：\n英文标签："

        # 创建任务队列
        task_queue = queue.Queue()
        for item in items:
            task_queue.put(item)

        # 创建结果队列
        result_queue = queue.Queue()

        # 创建处理函数
        def worker():
            while not task_queue.empty():
                try:
                    # 获取任务
                    item = task_queue.get(block=False)

                    # 准备提示词
                    prompt = translation_prompt_template.format(
                        prompt=item.get("prompt", ""),
                        tags=item.get("tags", "")
                    )

                    # 定义生成函数
                    def generate_content_with_retry():
                        return self.generate_content(prompt)

                    # 定义重试条件：只对资源耗尽错误进行重试
                    def retry_condition(e):
                        return isinstance(e, AIModelError) and ("429" in str(e) or "RESOURCE_EXHAUSTED" in str(e) or "限速" in str(e))

                    # 定义重试回调
                    def on_retry(retry_count, delay, exception):
                        logger.warning(f"翻译时遇到API限速错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")

                    # 使用通用重试函数执行生成
                    result = retry_with_exponential_backoff(
                        func=generate_content_with_retry,
                        max_retries=5,
                        base_delay=2.0,
                        retry_exceptions=(Exception,),
                        retry_condition=retry_condition,
                        on_retry=on_retry
                    )

                    # 解析结果
                    en_prompt = ""
                    en_tags = ""

                    if result:
                        lines = result.split('\n')
                        for line in lines:
                            if line.startswith("英文提示词："):
                                en_prompt = line[6:].strip()
                            elif line.startswith("英文标签："):
                                en_tags = line[5:].strip()

                    # 更新项目
                    item["en_prompt"] = en_prompt if en_prompt else item.get("prompt", "")
                    item["en_tags"] = en_tags if en_tags else item.get("tags", "")

                    # 添加到结果队列
                    result_queue.put(item)

                    # 更新进度
                    if progress_callback:
                        progress_callback(result_queue.qsize(), len(items))
                except queue.Empty:
                    break
                except Exception as e:
                    logger.error(f"翻译失败: {e}")
                    # 如果翻译失败，将原始项目添加到结果队列
                    item["en_prompt"] = item.get("prompt", "")
                    item["en_tags"] = item.get("tags", "")
                    result_queue.put(item)

                    # 更新进度
                    if progress_callback:
                        progress_callback(result_queue.qsize(), len(items))
                finally:
                    # 标记任务完成
                    task_queue.task_done()

        # 创建和启动线程
        threads = []
        for _ in range(min(max_threads, len(items))):
            t = threading.Thread(target=worker)
            t.daemon = True
            t.start()
            threads.append(t)

        # 等待所有任务完成
        for t in threads:
            t.join()

        # 收集结果
        results = []
        while not result_queue.empty():
            results.append(result_queue.get())

        # 按原始顺序排序结果
        results.sort(key=lambda x: x.get("id", 0))

        return results

    def analyze_video_content(self, video_url: str, prompt: str = None, tags: str = None) -> Dict[str, Any]:
        """
        使用Gemini分析YouTube视频内容，判断视频是否与提示词和标签相关

        Args:
            video_url: YouTube视频URL
            prompt: 提示词
            tags: 标签

        Returns:
            Dict[str, Any]: 分析结果，包含相关性评分和优化建议
        """
        if not self._gemini_client:
            logger.error("Gemini模型未初始化")
            raise AIModelError("Gemini模型未初始化")

        try:
            # 准备分析提示词
            analysis_prompt = f"""
            请分析这个YouTube视频的内容，并判断它是否与以下提示词和标签相关。

            提示词: {prompt or "无"}
            标签: {tags or "无"}

            请提供以下信息:
            1. 视频内容简要描述（不超过100字）
            2. 相关性评分（0-100分）
            3. 相关性分析（为什么相关或不相关）
            4. 如果相关性低于70分，请提供3个优化后的搜索关键词，这些关键词应该能找到更相关的视频

            以JSON格式返回结果，包含以下字段:
            {{
                "description": "视频内容描述",
                "relevance_score": 分数,
                "analysis": "相关性分析",
                "is_relevant": true/false,
                "optimized_keywords": ["关键词1", "关键词2", "关键词3"]  // 仅当相关性低时提供
            }}
            """

            # 获取模型配置
            model_name = self._current_model_config.get("model_name", "gemini-2.0-flash")

            # 创建视频部分
            # 检查视频URL格式
            if not video_url.startswith("http"):
                logger.warning(f"视频URL格式不正确: {video_url}")
                raise ValueError("视频URL必须以http或https开头")

            # 根据URL确定MIME类型
            mime_type = "video/mp4"  # 默认MIME类型

            # 根据URL后缀确定MIME类型
            if video_url.lower().endswith(".mp4"):
                mime_type = "video/mp4"
            elif video_url.lower().endswith(".webm"):
                mime_type = "video/webm"
            elif video_url.lower().endswith(".avi"):
                mime_type = "video/x-msvideo"
            elif video_url.lower().endswith(".mov"):
                mime_type = "video/quicktime"
            elif video_url.lower().endswith(".wmv"):
                mime_type = "video/x-ms-wmv"
            elif video_url.lower().endswith(".flv"):
                mime_type = "video/x-flv"
            elif video_url.lower().endswith(".mkv"):
                mime_type = "video/x-matroska"

            logger.info(f"视频URL: {video_url}")
            logger.info(f"使用MIME类型: {mime_type}")

            # 使用Part.from_uri代替FileData，正确的参数是file_uri
            try:
                video_part = types.Part.from_uri(file_uri=video_url, mime_type=mime_type)
                logger.info("成功创建视频Part对象")
            except Exception as e:
                logger.error(f"创建视频Part对象失败: {e}")
                raise ValueError(f"无法处理视频URL: {e}")

            # 创建生成配置
            config = types.GenerateContentConfig(
                temperature=0.2,  # 使用较低的温度以获得更确定性的结果
                max_output_tokens=2048,
                response_mime_type="application/json"  # 请求JSON格式的响应
            )

            # 定义生成函数
            def generate_video_analysis():
                return self._gemini_client.models.generate_content(
                    model=model_name,
                    contents=[video_part, analysis_prompt],
                    config=config
                )

            # 定义重试条件：只对资源耗尽错误进行重试
            def retry_condition(e):
                return isinstance(e, ResourceExhausted) or "429" in str(e) or "RESOURCE_EXHAUSTED" in str(e) or "限速" in str(e)

            # 定义重试回调
            def on_retry(retry_count, delay, exception):
                logger.warning(f"视频分析时遇到API限速错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")

            # 使用通用重试函数执行生成
            response = retry_with_exponential_backoff(
                func=generate_video_analysis,
                max_retries=3,
                base_delay=2.0,
                retry_exceptions=(Exception,),
                retry_condition=retry_condition,
                on_retry=on_retry
            )

            # 解析JSON响应
            try:
                # 记录原始响应
                logger.info(f"API响应: {response.text[:200]}...")

                # 尝试直接解析JSON
                result = json.loads(response.text)
                logger.info("成功直接解析JSON响应")
            except json.JSONDecodeError:
                logger.warning("直接解析JSON失败，尝试从文本中提取JSON部分")
                # 如果直接解析失败，尝试从文本中提取JSON部分
                text = response.text

                # 尝试多种JSON提取方法
                # 方法1: 查找第一个{和最后一个}
                json_start = text.find('{')
                json_end = text.rfind('}') + 1

                if json_start >= 0 and json_end > json_start:
                    json_str = text[json_start:json_end]
                    logger.info(f"提取的JSON字符串: {json_str[:200]}...")
                    try:
                        result = json.loads(json_str)
                        logger.info("成功解析提取的JSON")
                    except json.JSONDecodeError:
                        logger.warning("解析提取的JSON失败，尝试使用正则表达式")

                        # 方法2: 使用正则表达式查找JSON对象
                        import re
                        json_pattern = r'\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\}'
                        matches = re.findall(json_pattern, text)

                        if matches:
                            # 尝试解析找到的每个JSON对象
                            for potential_json in matches:
                                try:
                                    result = json.loads(potential_json)
                                    # 检查是否包含必要的字段
                                    if "description" in result and "relevance_score" in result:
                                        logger.info("使用正则表达式成功提取并解析JSON")
                                        break
                                except json.JSONDecodeError:
                                    continue
                            else:
                                # 如果所有潜在JSON都解析失败，创建基本结构
                                logger.warning("所有JSON提取方法都失败，使用基本结构")
                                result = self._create_default_video_analysis_result("无法解析视频内容")
                        else:
                            # 没有找到JSON对象，创建基本结构
                            logger.warning("未找到JSON对象，使用基本结构")
                            result = self._create_default_video_analysis_result("无法解析视频内容")
                else:
                    # 找不到JSON结构，返回基本结构
                    logger.warning("未找到JSON结构，使用基本结构")
                    result = self._create_default_video_analysis_result("无法解析视频内容")

            # 确保结果包含所有必要字段
            if "relevance_score" not in result:
                result["relevance_score"] = 0
            if "is_relevant" not in result:
                result["is_relevant"] = result["relevance_score"] >= 70
            if "optimized_keywords" not in result and result["relevance_score"] < 70:
                result["optimized_keywords"] = []

            return result
        except Exception as e:
            logger.error(f"视频内容分析失败: {e}")
            return self._create_default_video_analysis_result(str(e))

    def _create_default_video_analysis_result(self, reason: str = "未知原因") -> Dict[str, Any]:
        """
        创建默认的视频分析结果

        Args:
            reason: 失败原因

        Returns:
            Dict[str, Any]: 默认的分析结果
        """
        return {
            "description": f"无法解析视频内容: {reason}",
            "relevance_score": 0,
            "analysis": "分析失败，无法确定相关性",
            "is_relevant": False,
            "optimized_keywords": []
        }

    def analyze_screenplay(self, text: str,
                          progress_callback: Callable = None) -> List[Dict[str, str]]:
        """
        分析文案，生成分镜

        Args:
            text: 文案文本
            progress_callback: 进度回调函数，接收参数(current, total, status)

        Returns:
            List[Dict[str, str]]: 分镜列表
        """
        if not text:
            return []

        # 更新进度：开始分析
        if progress_callback:
            progress_callback(0, 100, "正在分析文案...")

        # 加载分镜提示词
        screenplay_prompt_template = ""
        if self.config_manager:
            screenplay_prompt_template = self.config_manager.get("prompt_settings.screenplay_prompt", "")

        if not screenplay_prompt_template:
            screenplay_prompt_template = "我需要将以下文案分解成多个分镜，每个分镜时长不少于15秒，确保每个分镜包含完整的一个或多个句子。对于每个分镜，请提供以下信息：\n1. 原文：直接从文案中提取的文本。\n2. 中文提示词：提取关键元素和主题，便于搜索视频素材。\n3. 标签：3-5个关键词，用逗号分隔。\n\n请确保分镜划分合理，每个分镜主题集中，长度适中。文案内容如下：\n\n{text}"

        # 准备提示词
        prompt = screenplay_prompt_template.format(text=text)

        logger.info(f"分析文案，文本长度: {len(text)}字符")
        logger.info(f"当前模型类型: {self._current_model_config.get('api_type', '')}")
        logger.info(f"Gemini客户端状态: {'已初始化' if self._gemini_client else '未初始化'}")

        # 调用AI模型分析
        try:
            # 更新进度：AI处理中
            if progress_callback:
                progress_callback(10, 100, "AI正在处理...")

            # 使用流式输出以获取实时进度
            def stream_callback(chunk_text, full_text):
                # 根据已生成的文本长度估计进度
                # 假设最终文本长度约为输入文本长度的2倍
                estimated_total_length = len(text) * 2
                current_length = len(full_text)
                progress = min(90, 10 + int(80 * current_length / estimated_total_length))

                if progress_callback:
                    progress_callback(progress, 100, "正在生成分镜...")

            logger.info("开始调用generate_content生成分镜")

            # 定义生成函数
            def generate_screenplay_content():
                return self.generate_content(prompt, stream=True, callback=stream_callback)

            # 定义重试条件：只对资源耗尽错误进行重试
            def retry_condition(e):
                return isinstance(e, AIModelError) and ("429" in str(e) or "RESOURCE_EXHAUSTED" in str(e) or "限速" in str(e))

            # 定义重试回调
            def on_retry(retry_count, delay, exception):
                logger.warning(f"分析文案时遇到API限速错误，将在{delay:.2f}秒后进行第{retry_count}次重试: {exception}")
                if progress_callback:
                    progress_callback(10, 100, f"API限速，等待重试({retry_count}/5)...")

            try:
                # 使用通用重试函数执行生成
                result = retry_with_exponential_backoff(
                    func=generate_screenplay_content,
                    max_retries=5,
                    base_delay=2.0,
                    retry_exceptions=(Exception,),
                    retry_condition=retry_condition,
                    on_retry=on_retry
                )
            except Exception as e:
                if progress_callback:
                    progress_callback(100, 100, f"分析失败: {e}")
                raise

            logger.info(f"generate_content调用完成，结果长度: {len(result) if result else 0}字符")

            # 更新进度：解析结果
            if progress_callback:
                progress_callback(95, 100, "正在解析结果...")

            # 解析结果
            shots = []
            current_shot = {}
            section = None

            if result:
                logger.info(f"开始解析生成结果，长度: {len(result)}字符")
                logger.info(f"结果前100个字符: {result[:100]}")

                lines = result.split('\n')
                logger.info(f"分割为{len(lines)}行")

                # 如果没有明确的分镜标记，尝试将整个文本作为一个分镜
                has_shot_markers = False

                for line in lines:
                    line = line.strip()

                    # 跳过空行
                    if not line:
                        continue

                    # 检测新的分镜
                    if (line.startswith("1.") or line.startswith("【") or
                        line.startswith("分镜") or line.startswith("场景") or
                        line.startswith("**分镜") or  # 添加对**分镜N**格式的支持
                        (line.startswith("原文") and not current_shot.get("text")) or
                        (line.startswith("*   **原文") and not current_shot.get("text"))):  # 添加对*   **原文:**格式的支持

                        has_shot_markers = True
                        logger.info(f"检测到新分镙标记: {line[:30]}")

                        # 如果已有分镜，则添加到列表
                        if current_shot.get("text"):
                            shots.append(current_shot)
                            logger.info(f"添加分镜 #{len(shots)}, 文本长度: {len(current_shot.get('text', ''))}")

                        # 创建新分镜
                        current_shot = {
                            "id": len(shots) + 1,
                            "text": "",
                            "prompt": "",
                            "tags": "",
                            "en_prompt": "",
                            "en_tags": "",
                            "video_url": ""
                        }

                        # 如果是标题行，则提取标题
                        if line.startswith("【") and "】" in line:
                            current_shot["title"] = line
                            section = None
                        else:
                            section = "text"

                    # 检测分段
                    elif line.startswith("原文：") or line.startswith("原文:") or line.startswith("*   **原文：") or line.startswith("*   **原文:"):
                        section = "text"
                        if line.startswith("*   **原文"):
                            # 处理Markdown格式
                            line = line.replace("*   **原文：", "").replace("*   **原文:", "").replace("**", "").strip()
                        else:
                            line = line[3:].strip()
                        logger.info(f"检测到原文标记: {line[:30]}")
                    elif (line.startswith("中文提示词：") or line.startswith("中文提示词:") or
                          line.startswith("提示词：") or line.startswith("提示词:") or
                          line.startswith("*   **中文提示词：") or line.startswith("*   **中文提示词:") or
                          line.startswith("*   **提示词：") or line.startswith("*   **提示词:")):
                        section = "prompt"
                        if line.startswith("*   **"):
                            # 处理Markdown格式
                            if "中文提示词" in line:
                                line = line.replace("*   **中文提示词：", "").replace("*   **中文提示词:", "").replace("**", "").strip()
                            else:
                                line = line.replace("*   **提示词：", "").replace("*   **提示词:", "").replace("**", "").strip()
                        else:
                            line = line[5:].strip() if line.startswith("提示词") else line[6:].strip()
                        logger.info(f"检测到提示词标记: {line[:30]}")
                    elif line.startswith("标签：") or line.startswith("标签:") or line.startswith("*   **标签：") or line.startswith("*   **标签:"):
                        section = "tags"
                        if line.startswith("*   **标签"):
                            # 处理Markdown格式
                            line = line.replace("*   **标签：", "").replace("*   **标签:", "").replace("**", "").strip()
                        else:
                            line = line[3:].strip()
                        logger.info(f"检测到标签标记: {line[:30]}")

                    # 根据当前分段添加内容
                    if section:
                        # 跳过Markdown格式的标记行，但不跳过已经处理过的内容
                        if (line.startswith("**分镜") or
                            (line.startswith("*   **") and "原文" not in line and "提示词" not in line and "标签" not in line)):
                            continue

                        if section == "text" and line and not line.startswith("中文提示词") and not line.startswith("标签") and not line.startswith("*   **中文") and not line.startswith("*   **标签"):
                            current_shot["text"] += line + " "
                            logger.info(f"添加文本: {line[:30]}...")
                        elif section == "prompt" and line and not line.startswith("标签") and not line.startswith("*   **标签"):
                            current_shot["prompt"] += line + " "
                            logger.info(f"添加提示词: {line[:30]}...")
                        elif section == "tags" and line:
                            current_shot["tags"] += line + " "
                            logger.info(f"添加标签: {line[:30]}...")

                # 添加最后一个分镜（无论是否检测到分镜标记）
                if current_shot.get("text"):
                    shots.append(current_shot)
                    logger.info(f"添加最后一个分镜 #{len(shots)}, 文本长度: {len(current_shot.get('text', ''))}")

                # 如果没有检测到分镜标记或没有成功解析出分镜，尝试将整个文本作为一个分镜
                if not shots:
                    logger.info("未能解析出分镜，尝试将整个文本作为一个分镜")
                    # 尝试从文本中提取关键信息
                    text_parts = result.split("提示词：", 1)

                    if len(text_parts) > 1:
                        # 有提示词部分
                        text = text_parts[0].strip()
                        remaining = text_parts[1]

                        tags_parts = remaining.split("标签：", 1)
                        if len(tags_parts) > 1:
                            prompt = tags_parts[0].strip()
                            tags = tags_parts[1].strip()
                        else:
                            prompt = remaining.strip()
                            tags = ""
                    else:
                        # 没有明确的提示词部分，将整个文本作为原文
                        text = result.strip()
                        prompt = "自动生成的提示词"
                        tags = "自动生成"

                    shots = [{
                        "id": 1,
                        "text": text,
                        "prompt": prompt,
                        "tags": tags,
                        "en_prompt": "",
                        "en_tags": "",
                        "video_url": ""
                    }]

                # 清理数据
                for shot in shots:
                    # 清理并提取数据
                    text_parts = shot["text"].strip().split("2.  **中文提示词：**")

                    if len(text_parts) > 1:
                        # 提取原文
                        text = text_parts[0]
                        # 去除各种可能的原文前缀格式
                        text = text.replace("1.  **原文：** ", "")
                        text = text.replace("1. **原文：** ", "")
                        text = text.replace("1.  原文： ", "")
                        text = text.replace("1. 原文： ", "")
                        text = text.replace("**原文：** ", "")
                        text = text.replace("原文： ", "")
                        text = text.replace("*   **原文：** ", "")
                        text = text.replace("*   **原文:** ", "")
                        text = text.replace("*   原文: ", "")
                        text = text.replace("*   原文：", "")
                        text = text.replace("**", "")
                        text = text.strip()

                        # 提取提示词和标签
                        remaining = text_parts[1]
                        prompt_tags = remaining.split("3.  **标签：**")

                        if len(prompt_tags) > 1:
                            prompt = prompt_tags[0].replace("**", "").strip()
                            tags = prompt_tags[1].replace("**", "").strip()
                        else:
                            prompt = remaining.replace("**", "").strip()
                            tags = shot["tags"].strip().replace("3.  **标签：** ", "").replace("**", "")
                    else:
                        # 如果没有明确的分隔，保持原样但清理前缀
                        text = shot["text"]
                        # 去除各种可能的原文前缀格式
                        text = text.replace("1.  **原文：** ", "")
                        text = text.replace("1. **原文：** ", "")
                        text = text.replace("1.  原文： ", "")
                        text = text.replace("1. 原文： ", "")
                        text = text.replace("**原文：** ", "")
                        text = text.replace("原文： ", "")
                        text = text.replace("*   **原文：** ", "")
                        text = text.replace("*   **原文:** ", "")
                        text = text.replace("*   原文: ", "")
                        text = text.replace("*   原文：", "")
                        text = text.replace("**", "")
                        text = text.strip()

                        prompt = shot["prompt"].replace("2.  **中文提示词：** ", "").replace("**", "").strip()
                        tags = shot["tags"].replace("3.  **标签：** ", "").replace("**", "").strip()

                    # 更新分镜数据
                    shot["text"] = text
                    shot["prompt"] = prompt
                    shot["tags"] = tags

                    # 记录清理后的数据
                    logger.info(f"清理后的分镜 #{shot.get('id')}: 文本长度={len(text)}, 提示词长度={len(prompt)}, 标签长度={len(tags)}")

                logger.info(f"解析完成，共生成{len(shots)}个分镜")

                # 如果没有成功解析出分镜，创建一个默认分镜
                if not shots:
                    logger.warning("未能解析出分镜，创建默认分镜")
                    shots = [{
                        "id": 1,
                        "text": result[:500],  # 使用生成结果的前500个字符作为文本
                        "prompt": "自动生成的提示词",
                        "tags": "自动生成",
                        "en_prompt": "",
                        "en_tags": "",
                        "video_url": ""
                    }]

            # 更新进度：完成
            if progress_callback:
                progress_callback(100, 100, "分析完成")

            return shots
        except Exception as e:
            logger.error(f"分析文案失败: {e}")

            # 更新进度：失败
            if progress_callback:
                progress_callback(100, 100, f"分析失败: {e}")

            raise AIModelError(f"分析文案失败: {e}")