#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终BUG修复验证脚本
验证urllib错误修复和图片实时显示功能
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer

from config import ConfigManager
from ui.main_window import MainWindow

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("BugFixFinalTest")

class FinalBugFixTester:
    """最终BUG修复测试器"""
    
    def __init__(self):
        self.app = QApplication.instance() or QApplication(sys.argv)
        self.config_manager = ConfigManager()
        self.main_window = None
        
    def test_urllib_import_fix(self):
        """测试urllib导入修复"""
        print("🔧 测试urllib导入修复...")
        
        try:
            from modules.image_downloader import ImageDownloader
            
            # 创建图片下载器实例
            downloader = ImageDownloader(self.config_manager)
            
            # 测试包含URL解析的方法
            test_url = "https://www.google.com/search?q=test&tbm=isch"
            
            # 模拟URL解析过程
            try:
                from urllib.parse import urlparse, parse_qs
                parsed_url = urlparse(test_url)
                query_params = parse_qs(parsed_url.query)
                search_query = query_params.get('q', [''])[0]
                
                if search_query == "test":
                    print("  ✅ urllib.parse导入和使用正常")
                    return True
                else:
                    print("  ❌ URL解析结果错误")
                    return False
                    
            except Exception as e:
                print(f"  ❌ urllib.parse使用失败: {e}")
                return False
                
        except Exception as e:
            print(f"  ❌ 图片下载器创建失败: {e}")
            return False
    
    def test_image_callback_mechanism(self):
        """测试图片回调机制"""
        print("\n📸 测试图片回调机制...")
        
        try:
            from modules.image_downloader import ImageDownloader
            
            downloader = ImageDownloader(self.config_manager)
            
            # 检查search_and_download方法是否支持image_callback参数
            import inspect
            sig = inspect.signature(downloader.search_and_download)
            
            if 'image_callback' in sig.parameters:
                print("  ✅ search_and_download方法支持image_callback参数")
            else:
                print("  ❌ search_and_download方法不支持image_callback参数")
                return False
            
            # 检查batch_download_for_shots方法是否支持image_callback参数
            sig = inspect.signature(downloader.batch_download_for_shots)
            
            if 'image_callback' in sig.parameters:
                print("  ✅ batch_download_for_shots方法支持image_callback参数")
                return True
            else:
                print("  ❌ batch_download_for_shots方法不支持image_callback参数")
                return False
                
        except Exception as e:
            print(f"  ❌ 图片回调机制测试失败: {e}")
            return False
    
    def test_real_time_update_mechanism(self):
        """测试实时更新机制"""
        print("\n🔄 测试实时更新机制...")
        
        try:
            # 创建主窗口
            self.main_window = MainWindow(self.config_manager)
            
            # 检查是否有_on_image_downloaded方法
            if hasattr(self.main_window, '_on_image_downloaded'):
                print("  ✅ 主窗口有_on_image_downloaded方法")
            else:
                print("  ❌ 主窗口缺少_on_image_downloaded方法")
                return False
            
            # 检查shot_list是否有update_shot_image方法
            if hasattr(self.main_window.shot_list, 'update_shot_image'):
                print("  ✅ 分镜列表有update_shot_image方法")
            else:
                print("  ❌ 分镜列表缺少update_shot_image方法")
                return False
            
            # 检查shot_list是否有refresh_images方法
            if hasattr(self.main_window.shot_list, 'refresh_images'):
                print("  ✅ 分镜列表有refresh_images方法")
                return True
            else:
                print("  ❌ 分镜列表缺少refresh_images方法")
                return False
                
        except Exception as e:
            print(f"  ❌ 实时更新机制测试失败: {e}")
            return False
    
    def test_alternative_image_apis(self):
        """测试备用图片API"""
        print("\n🌐 测试备用图片API...")
        
        try:
            from modules.image_downloader import ImageDownloader
            
            downloader = ImageDownloader(self.config_manager)
            
            # 测试Lorem Picsum
            print("  📸 测试Lorem Picsum API...")
            picsum_urls = downloader._get_picsum_images(1, 800, 600)
            if picsum_urls and picsum_urls[0].startswith("https://picsum.photos/"):
                print("    ✅ Lorem Picsum API正常")
            else:
                print("    ❌ Lorem Picsum API失败")
            
            # 测试本地备用图片
            print("  🖼️ 测试本地备用图片...")
            local_urls = downloader._get_local_fallback_images(1)
            if local_urls and local_urls[0].startswith("https://via.placeholder.com/"):
                print("    ✅ 本地备用图片正常")
            else:
                print("    ❌ 本地备用图片失败")
            
            # 测试综合API
            print("  🔄 测试综合API...")
            api_urls = downloader._get_alternative_image_apis(1, "test")
            if api_urls:
                print(f"    ✅ 综合API成功获取 {len(api_urls)} 个URL")
                return True
            else:
                print("    ❌ 综合API获取失败")
                return False
                
        except Exception as e:
            print(f"  ❌ 备用图片API测试失败: {e}")
            return False
    
    def test_error_handling_improvements(self):
        """测试错误处理改进"""
        print("\n🛡️ 测试错误处理改进...")
        
        try:
            from modules.image_downloader import ImageDownloader
            
            downloader = ImageDownloader(self.config_manager)
            
            # 检查新增的方法
            methods_to_check = [
                '_extract_image_urls_from_page',
                '_extract_urls_from_dom',
                '_can_scroll_to_element',
                '_get_alternative_image_apis'
            ]
            
            missing_methods = []
            for method_name in methods_to_check:
                if not hasattr(downloader, method_name):
                    missing_methods.append(method_name)
            
            if missing_methods:
                print(f"  ❌ 缺少方法: {missing_methods}")
                return False
            else:
                print("  ✅ 所有错误处理方法都存在")
                return True
                
        except Exception as e:
            print(f"  ❌ 错误处理改进测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始最终BUG修复验证")
        print("=" * 60)
        
        tests = [
            ("urllib导入修复", self.test_urllib_import_fix),
            ("图片回调机制", self.test_image_callback_mechanism),
            ("实时更新机制", self.test_real_time_update_mechanism),
            ("备用图片API", self.test_alternative_image_apis),
            ("错误处理改进", self.test_error_handling_improvements),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 异常: {e}")
                logger.error(f"测试 {test_name} 时发生异常", exc_info=True)
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            print("🎉 所有BUG修复验证通过！")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关修复。")
            return False
    
    def show_summary(self):
        """显示修复总结"""
        print("\n📋 BUG修复总结:")
        print("=" * 60)
        
        print("🔧 问题1: urllib变量错误")
        print("  ✅ 修复: 改用 from urllib.parse import urlparse, parse_qs")
        print("  ✅ 效果: 解决 'cannot access local variable urllib' 错误")
        
        print("\n📸 问题2: 图片下载成功但不显示")
        print("  ✅ 修复: 添加实时图片回调机制")
        print("  ✅ 效果: 图片下载完成后立即在列表中显示")
        
        print("\n🌐 额外改进:")
        print("  ✅ 新增多个稳定的备用图片API")
        print("  ✅ 改进Google图片选择器，支持2024年新结构")
        print("  ✅ 增强错误处理和重试机制")
        print("  ✅ 完善日志记录和状态反馈")
        
        print("\n💡 使用建议:")
        print("  - 图片下载现在会实时显示在列表中")
        print("  - 如果Google搜索失败，会自动使用备用API")
        print("  - 所有操作都有详细的日志记录")
        print("  - 双击图片可查看原图")
    
    def cleanup(self):
        """清理资源"""
        if self.main_window:
            self.main_window.close()

def main():
    """主函数"""
    tester = FinalBugFixTester()
    
    try:
        success = tester.run_all_tests()
        tester.show_summary()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.error("测试失败", exc_info=True)
        return 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
