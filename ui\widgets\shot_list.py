#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分镜列表控件
用于显示和管理分镜列表，支持编辑和排序。
"""

import os
import re
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QHeaderView,
    QAbstractItemView, QMenu, QMessageBox, QLabel, QDialog, QScrollArea
)
from PySide6.QtCore import Qt, Signal, Slot, QPoint
from PySide6.QtGui import QAction, QPixmap, QMouseEvent

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ShotListWidget")


class ClickableImageLabel(QLabel):
    """
    可点击的图片标签，支持双击查看原图
    """

    # 定义信号
    doubleClicked = Signal(str)  # 双击信号，传递图片路径

    def __init__(self, image_path="", parent=None):
        """
        初始化可点击图片标签

        Args:
            image_path: 图片路径
            parent: 父窗口
        """
        super().__init__(parent)
        self.image_path = image_path
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
                padding: 2px;
            }
            QLabel:hover {
                border: 2px solid #007acc;
                background-color: #e6f3ff;
            }
        """)

        # 设置鼠标指针
        self.setCursor(Qt.PointingHandCursor)

        # 设置工具提示
        if image_path:
            self.setToolTip(f"双击查看原图\n路径: {image_path}")
        else:
            self.setToolTip("暂无图片")

    def mouseDoubleClickEvent(self, event):
        """
        处理双击事件

        Args:
            event: 鼠标事件
        """
        if event.button() == Qt.LeftButton and self.image_path:
            self.doubleClicked.emit(self.image_path)
        super().mouseDoubleClickEvent(event)

    def set_image(self, image_path):
        """
        设置图片

        Args:
            image_path: 图片路径
        """
        self.image_path = image_path

        if image_path and os.path.exists(image_path):
            # 加载图片
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # 缩放图片以适应更大的单元格
                pixmap = pixmap.scaled(160, 90, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.setPixmap(pixmap)
                self.setToolTip(f"双击查看原图\n路径: {image_path}")
            else:
                self.setText("图片加载失败")
                self.setToolTip(f"图片加载失败\n路径: {image_path}")
        else:
            self.setText("暂无图片")
            self.setToolTip("暂无图片")


class ImageViewerDialog(QDialog):
    """
    图片查看器对话框
    """

    def __init__(self, image_path, parent=None):
        """
        初始化图片查看器

        Args:
            image_path: 图片路径
            parent: 父窗口
        """
        super().__init__(parent)
        self.image_path = image_path
        self.setWindowTitle(f"图片查看器 - {os.path.basename(image_path)}")
        self.setModal(True)
        self.resize(800, 600)

        # 设置窗口标志
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint | Qt.WindowMaximizeButtonHint)

        self._setup_ui()
        self._load_image()

    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setAlignment(Qt.AlignCenter)

        # 创建图片标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("QLabel { background-color: white; }")

        # 设置滚动区域的内容
        scroll_area.setWidget(self.image_label)

        # 添加到布局
        layout.addWidget(scroll_area)

    def _load_image(self):
        """加载图片"""
        if self.image_path and os.path.exists(self.image_path):
            pixmap = QPixmap(self.image_path)
            if not pixmap.isNull():
                # 获取屏幕尺寸
                screen = self.screen()
                screen_size = screen.availableGeometry().size()

                # 计算合适的显示尺寸（不超过屏幕的80%）
                max_width = int(screen_size.width() * 0.8)
                max_height = int(screen_size.height() * 0.8)

                # 如果图片太大，则缩放
                if pixmap.width() > max_width or pixmap.height() > max_height:
                    pixmap = pixmap.scaled(max_width, max_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)

                self.image_label.setPixmap(pixmap)

                # 调整对话框大小以适应图片
                self.resize(pixmap.width() + 40, pixmap.height() + 60)
            else:
                self.image_label.setText("图片加载失败")
        else:
            self.image_label.setText("图片文件不存在")

    def keyPressEvent(self, event):
        """处理按键事件"""
        if event.key() == Qt.Key_Escape:
            self.close()
        super().keyPressEvent(event)

class ShotListWidget(QWidget):
    """
    分镜列表控件，用于显示和管理分镜列表
    """

    # 定义信号
    shots_changed = Signal(list)  # 分镜变更信号

    # 列索引常量
    INDEX_COLUMN = 0
    TEXT_COLUMN = 1
    PROMPT_COLUMN = 2
    EN_PROMPT_COLUMN = 3
    IMAGE_COLUMN = 4
    VIDEO_URL_COLUMN = 5

    def __init__(self, parent=None):
        """
        初始化分镜列表控件

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 分镜数据
        self.shots = []

        # 设置UI
        self._setup_ui()

    def _setup_ui(self):
        """设置UI"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(6)  # 添加序号列，移除视频分析结果列
        self.table.setHorizontalHeaderLabels(["序号", "原文", "中文提示词", "英文提示词", "图片", "视频地址"])

        # 隐藏垂直表头（自带的行号）
        self.table.verticalHeader().setVisible(False)

        # 设置表格属性
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setEditTriggers(QAbstractItemView.DoubleClicked | QAbstractItemView.EditKeyPressed)
        self.table.setAlternatingRowColors(True)
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self._show_context_menu)

        # 设置行高 - 增大到100像素
        self.table.verticalHeader().setDefaultSectionSize(100)  # 增大行高到100像素

        # 设置表格列宽
        header = self.table.horizontalHeader()
        # 序号列固定宽度
        header.setSectionResizeMode(self.INDEX_COLUMN, QHeaderView.Fixed)
        self.table.setColumnWidth(self.INDEX_COLUMN, 60)  # 设置序号列宽度为60像素

        # 其他列自适应
        header.setSectionResizeMode(self.TEXT_COLUMN, QHeaderView.Stretch)
        header.setSectionResizeMode(self.PROMPT_COLUMN, QHeaderView.Stretch)
        header.setSectionResizeMode(self.EN_PROMPT_COLUMN, QHeaderView.Stretch)

        # 图片列固定宽度 - 增大到180像素
        header.setSectionResizeMode(self.IMAGE_COLUMN, QHeaderView.Fixed)
        self.table.setColumnWidth(self.IMAGE_COLUMN, 180)  # 增大图片列宽度到180像素

        header.setSectionResizeMode(self.VIDEO_URL_COLUMN, QHeaderView.Stretch)

        # 添加到布局
        layout.addWidget(self.table)

        # 连接信号
        self.table.itemChanged.connect(self._on_item_changed)

    def set_shots(self, shots: List[Dict[str, Any]]):
        """
        设置分镜列表

        Args:
            shots: 分镜列表
        """
        # 更新分镜数据
        self.shots = shots

        # 断开信号连接，避免在更新表格时触发事件
        self.table.itemChanged.disconnect(self._on_item_changed)

        # 更新表格
        self._update_table()

        # 重新连接信号
        self.table.itemChanged.connect(self._on_item_changed)

    def clear_shots(self):
        """清空分镜列表"""
        # 清空分镜数据
        self.shots = []

        # 清空表格
        self.table.clearContents()
        self.table.setRowCount(0)

    def get_shots(self) -> List[Dict[str, Any]]:
        """
        获取分镜列表

        Returns:
            List[Dict[str, Any]]: 分镜列表
        """
        return self.shots

    def update_shot_image(self, shot_index: int, image_path: str):
        """
        更新指定分镜的图片

        Args:
            shot_index: 分镜索引
            image_path: 新的图片路径
        """
        if 0 <= shot_index < len(self.shots):
            # 更新分镜数据
            self.shots[shot_index]["image_path"] = image_path

            # 更新表格中的图片显示
            if shot_index < self.table.rowCount():
                # 获取当前的图片标签
                image_widget = self.table.cellWidget(shot_index, self.IMAGE_COLUMN)
                if isinstance(image_widget, ClickableImageLabel):
                    # 更新图片
                    image_widget.set_image(image_path)
                else:
                    # 如果不是ClickableImageLabel，创建新的
                    image_label = ClickableImageLabel(image_path)
                    image_label.doubleClicked.connect(self._on_image_double_clicked)
                    image_label.set_image(image_path)
                    self.table.setCellWidget(shot_index, self.IMAGE_COLUMN, image_label)

            # 发射分镜变更信号
            self.shots_changed.emit(self.shots)

    def refresh_images(self):
        """
        刷新所有图片显示
        """
        for row in range(self.table.rowCount()):
            if row < len(self.shots):
                shot = self.shots[row]
                image_path = shot.get("image_path", "")

                # 获取当前的图片标签
                image_widget = self.table.cellWidget(row, self.IMAGE_COLUMN)
                if isinstance(image_widget, ClickableImageLabel):
                    # 更新图片
                    image_widget.set_image(image_path)
                else:
                    # 如果不是ClickableImageLabel，创建新的
                    image_label = ClickableImageLabel(image_path)
                    image_label.doubleClicked.connect(self._on_image_double_clicked)
                    image_label.set_image(image_path)
                    self.table.setCellWidget(row, self.IMAGE_COLUMN, image_label)

    def _update_table(self):
        """更新表格"""
        # 清空表格
        self.table.clearContents()
        self.table.setRowCount(len(self.shots))

        # 添加分镜数据
        for row, shot in enumerate(self.shots):
            # 添加序号列 - 不带#号
            index_item = QTableWidgetItem(f"{shot.get('id', row + 1)}")
            index_item.setTextAlignment(Qt.AlignCenter)  # 居中显示
            # 设置为只读
            index_item.setFlags(index_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, self.INDEX_COLUMN, index_item)

            # 原文 - 去除前缀
            text = shot.get("text", "")
            # 去除常见的原文前缀
            text = re.sub(r'^\(\d+\.\s*原文：\)', '', text)
            text = re.sub(r'^\*\s*原文:', '', text)
            text = re.sub(r'^\*+\s*\*+原文\**:*', '', text)  # 匹配更多的*原文*:格式
            text = re.sub(r'^\*+\s*', '', text)  # 去除开头的所有*号
            text = re.sub(r'^原文[:：]', '', text)  # 去除"原文:"前缀
            text = text.strip()

            text_item = QTableWidgetItem(text)
            text_item.setToolTip(text)  # 添加工具提示，方便查看完整内容
            self.table.setItem(row, self.TEXT_COLUMN, text_item)

            # 中文提示词
            prompt_item = QTableWidgetItem(shot.get("prompt", ""))
            prompt_item.setToolTip(shot.get("prompt", ""))
            self.table.setItem(row, self.PROMPT_COLUMN, prompt_item)

            # 英文提示词
            en_prompt_item = QTableWidgetItem(shot.get("en_prompt", ""))
            en_prompt_item.setToolTip(shot.get("en_prompt", ""))
            self.table.setItem(row, self.EN_PROMPT_COLUMN, en_prompt_item)

            # 图片 - 使用可点击的图片标签
            image_path = shot.get("image_path", "")
            image_label = ClickableImageLabel(image_path)

            # 连接双击信号
            image_label.doubleClicked.connect(self._on_image_double_clicked)

            # 设置图片
            image_label.set_image(image_path)

            # 设置单元格小部件
            self.table.setCellWidget(row, self.IMAGE_COLUMN, image_label)

            # 视频地址
            video_url_item = QTableWidgetItem(shot.get("video_url", ""))
            video_url_item.setToolTip(shot.get("video_url", ""))
            self.table.setItem(row, self.VIDEO_URL_COLUMN, video_url_item)

            # 视频分析结果列已移除

    @Slot(QTableWidgetItem)
    def _on_item_changed(self, item):
        """
        处理表格项变更

        Args:
            item: 变更的表格项
        """
        # 获取行索引和列索引
        row = item.row()
        column = item.column()

        # 更新分镜数据
        if column == self.INDEX_COLUMN:
            # 序号列是只读的，不需要更新数据
            pass
        elif column == self.TEXT_COLUMN:
            self.shots[row]["text"] = item.text()
        elif column == self.PROMPT_COLUMN:
            self.shots[row]["prompt"] = item.text()
        elif column == self.EN_PROMPT_COLUMN:
            self.shots[row]["en_prompt"] = item.text()
        # 图片列不需要在这里处理，因为它使用的是setCellWidget而不是setItem
        elif column == self.VIDEO_URL_COLUMN:
            self.shots[row]["video_url"] = item.text()

        # 更新工具提示
        item.setToolTip(item.text())

        # 发射分镜变更信号
        self.shots_changed.emit(self.shots)

    @Slot(str)
    def _on_image_double_clicked(self, image_path):
        """
        处理图片双击事件

        Args:
            image_path: 图片路径
        """
        try:
            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                QMessageBox.warning(self, "图片不存在", f"图片文件不存在:\n{image_path}")
                return

            # 创建并显示图片查看器
            viewer = ImageViewerDialog(image_path, self)
            viewer.exec()

        except Exception as e:
            logger.error(f"打开图片查看器失败: {e}")
            QMessageBox.critical(self, "错误", f"打开图片查看器失败:\n{e}")

    @Slot(QPoint)
    def _show_context_menu(self, pos):
        """
        显示上下文菜单

        Args:
            pos: 菜单位置
        """
        # 获取当前选中的行
        selected_rows = sorted(set(index.row() for index in self.table.selectedIndexes()))

        if not selected_rows:
            return

        # 创建上下文菜单
        menu = QMenu(self)

        # 添加菜单项
        edit_action = QAction("编辑", self)
        edit_action.triggered.connect(lambda: self._on_edit_shot(selected_rows[0]))
        menu.addAction(edit_action)

        # 视频分析功能已移除

        menu.addSeparator()

        move_up_action = QAction("上移", self)
        move_up_action.setEnabled(len(selected_rows) == 1 and selected_rows[0] > 0)
        move_up_action.triggered.connect(lambda: self._on_move_shot(selected_rows[0], -1))
        menu.addAction(move_up_action)

        move_down_action = QAction("下移", self)
        move_down_action.setEnabled(len(selected_rows) == 1 and selected_rows[0] < len(self.shots) - 1)
        move_down_action.triggered.connect(lambda: self._on_move_shot(selected_rows[0], 1))
        menu.addAction(move_down_action)

        menu.addSeparator()

        delete_action = QAction("删除", self)
        delete_action.triggered.connect(lambda: self._on_delete_shots(selected_rows))
        menu.addAction(delete_action)

        # 显示菜单
        menu.exec(self.table.viewport().mapToGlobal(pos))

    def _on_edit_shot(self, row):
        """
        编辑分镜

        Args:
            row: 行索引
        """
        # 获取分镜数据
        shot = self.shots[row]

        # 此处可以弹出编辑对话框
        # 目前仅跳转到单元格进行编辑
        self.table.setCurrentCell(row, self.TEXT_COLUMN)
        self.table.editItem(self.table.item(row, self.TEXT_COLUMN))

    # 视频分析功能已移除

    def _on_move_shot(self, row, direction):
        """
        移动分镜

        Args:
            row: 行索引
            direction: 移动方向，-1为上移，1为下移
        """
        # 计算目标位置
        target_row = row + direction

        # 检查目标位置是否有效
        if target_row < 0 or target_row >= len(self.shots):
            return

        # 交换分镜位置
        self.shots[row], self.shots[target_row] = self.shots[target_row], self.shots[row]

        # 断开信号连接，避免在更新表格时触发事件
        self.table.itemChanged.disconnect(self._on_item_changed)

        # 更新表格
        self._update_table()

        # 重新连接信号
        self.table.itemChanged.connect(self._on_item_changed)

        # 选中移动后的行
        self.table.selectRow(target_row)

        # 发射分镜变更信号
        self.shots_changed.emit(self.shots)

    def _on_delete_shots(self, rows):
        """
        删除分镜

        Args:
            rows: 行索引列表
        """
        # 确认删除
        if len(rows) == 1:
            message = f"确定要删除第 {rows[0] + 1} 个分镜吗？"
        else:
            message = f"确定要删除选中的 {len(rows)} 个分镜吗？"

        reply = QMessageBox.question(
            self,
            "确认删除",
            message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 从后往前删除，避免索引变化
        for row in sorted(rows, reverse=True):
            if 0 <= row < len(self.shots):
                self.shots.pop(row)

        # 断开信号连接，避免在更新表格时触发事件
        self.table.itemChanged.disconnect(self._on_item_changed)

        # 更新表格
        self._update_table()

        # 重新连接信号
        self.table.itemChanged.connect(self._on_item_changed)

        # 发射分镜变更信号
        self.shots_changed.emit(self.shots)