#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI模型优化测试脚本
用于验证优化效果和性能改进
"""

import time
import threading
import logging
from typing import List, Dict, Any
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.ai_model import AIModel
from config import ConfigManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("AIModelTest")

class AIModelTester:
    """AI模型测试器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.ai_model = AIModel(self.config_manager)
        self.test_results = []
    
    def test_basic_functionality(self) -> Dict[str, Any]:
        """测试基本功能"""
        logger.info("开始基本功能测试...")
        
        test_result = {
            "test_name": "basic_functionality",
            "start_time": time.time(),
            "success": False,
            "error": None,
            "response_time": 0,
            "response_length": 0
        }
        
        try:
            # 测试连接
            connection_success, connection_msg = self.ai_model.test_connection()
            if not connection_success:
                raise Exception(f"连接测试失败: {connection_msg}")
            
            # 测试简单内容生成
            test_prompt = "请简单介绍一下人工智能的发展历史，不超过100字。"
            start_time = time.time()
            
            response = self.ai_model.generate_content(test_prompt)
            
            test_result["response_time"] = time.time() - start_time
            test_result["response_length"] = len(response) if response else 0
            test_result["success"] = response is not None and len(response) > 0
            
            logger.info(f"基本功能测试完成: 成功={test_result['success']}, "
                       f"响应时间={test_result['response_time']:.2f}秒, "
                       f"响应长度={test_result['response_length']}字符")
            
        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"基本功能测试失败: {e}")
        
        test_result["end_time"] = time.time()
        self.test_results.append(test_result)
        return test_result
    
    def test_batch_translation(self) -> Dict[str, Any]:
        """测试批量翻译功能"""
        logger.info("开始批量翻译测试...")
        
        test_result = {
            "test_name": "batch_translation",
            "start_time": time.time(),
            "success": False,
            "error": None,
            "items_count": 0,
            "successful_translations": 0,
            "total_time": 0
        }
        
        try:
            # 准备测试数据
            test_items = [
                {"id": 1, "prompt": "美丽的日落景色", "tags": "日落,风景,自然"},
                {"id": 2, "prompt": "繁忙的城市街道", "tags": "城市,街道,交通"},
                {"id": 3, "prompt": "宁静的森林小径", "tags": "森林,小径,宁静"}
            ]
            
            test_result["items_count"] = len(test_items)
            start_time = time.time()
            
            # 执行批量翻译
            results = self.ai_model.batch_translate_optimized(
                test_items,
                progress_callback=lambda current, total, status: logger.info(f"翻译进度: {current}/{total} - {status}"),
                batch_size=2
            )
            
            test_result["total_time"] = time.time() - start_time
            
            # 检查结果
            successful_count = 0
            for result in results:
                if result.get("en_prompt") and result.get("en_tags"):
                    successful_count += 1
            
            test_result["successful_translations"] = successful_count
            test_result["success"] = successful_count == len(test_items)
            
            logger.info(f"批量翻译测试完成: 成功翻译={successful_count}/{len(test_items)}, "
                       f"总耗时={test_result['total_time']:.2f}秒")
            
        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"批量翻译测试失败: {e}")
        
        test_result["end_time"] = time.time()
        self.test_results.append(test_result)
        return test_result
    
    def test_performance_under_load(self, concurrent_requests: int = 3) -> Dict[str, Any]:
        """测试并发负载下的性能"""
        logger.info(f"开始并发负载测试，并发数: {concurrent_requests}...")
        
        test_result = {
            "test_name": "performance_under_load",
            "start_time": time.time(),
            "concurrent_requests": concurrent_requests,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0,
            "max_response_time": 0,
            "min_response_time": float('inf')
        }
        
        results = []
        threads = []
        
        def worker(thread_id: int):
            """工作线程"""
            try:
                start_time = time.time()
                prompt = f"线程{thread_id}的测试请求：请简单描述春天的特点。"
                response = self.ai_model.generate_content(prompt)
                response_time = time.time() - start_time
                
                results.append({
                    "thread_id": thread_id,
                    "success": response is not None,
                    "response_time": response_time,
                    "response_length": len(response) if response else 0
                })
                
            except Exception as e:
                results.append({
                    "thread_id": thread_id,
                    "success": False,
                    "error": str(e),
                    "response_time": time.time() - start_time
                })
        
        # 启动并发线程
        start_time = time.time()
        for i in range(concurrent_requests):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 分析结果
        successful_count = sum(1 for r in results if r.get("success", False))
        failed_count = len(results) - successful_count
        response_times = [r["response_time"] for r in results if "response_time" in r]
        
        test_result["successful_requests"] = successful_count
        test_result["failed_requests"] = failed_count
        test_result["total_time"] = time.time() - start_time
        
        if response_times:
            test_result["average_response_time"] = sum(response_times) / len(response_times)
            test_result["max_response_time"] = max(response_times)
            test_result["min_response_time"] = min(response_times)
        
        logger.info(f"并发负载测试完成: 成功={successful_count}, 失败={failed_count}, "
                   f"平均响应时间={test_result['average_response_time']:.2f}秒")
        
        test_result["end_time"] = time.time()
        self.test_results.append(test_result)
        return test_result
    
    def test_error_recovery(self) -> Dict[str, Any]:
        """测试错误恢复能力"""
        logger.info("开始错误恢复测试...")
        
        test_result = {
            "test_name": "error_recovery",
            "start_time": time.time(),
            "success": False,
            "error": None
        }
        
        try:
            # 测试无效配置的恢复
            original_config = self.ai_model._current_model_config.copy()
            
            # 临时设置无效配置
            self.ai_model._current_model_config["api_key"] = "invalid_key"
            
            # 尝试请求（应该失败）
            try:
                self.ai_model.generate_content("测试请求")
                test_result["error"] = "应该失败但没有失败"
            except Exception:
                # 预期的失败
                pass
            
            # 恢复正确配置
            self.ai_model._current_model_config = original_config
            self.ai_model._initialize_model()
            
            # 再次尝试请求（应该成功）
            response = self.ai_model.generate_content("恢复测试请求")
            test_result["success"] = response is not None
            
            logger.info(f"错误恢复测试完成: 成功={test_result['success']}")
            
        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"错误恢复测试失败: {e}")
        
        test_result["end_time"] = time.time()
        self.test_results.append(test_result)
        return test_result
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("开始运行所有测试...")
        
        overall_result = {
            "start_time": time.time(),
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "performance_stats": None
        }
        
        # 运行各项测试
        tests = [
            self.test_basic_functionality,
            self.test_batch_translation,
            lambda: self.test_performance_under_load(3),
            self.test_error_recovery
        ]
        
        for test_func in tests:
            try:
                result = test_func()
                overall_result["tests_run"] += 1
                if result.get("success", False):
                    overall_result["tests_passed"] += 1
                else:
                    overall_result["tests_failed"] += 1
            except Exception as e:
                logger.error(f"测试执行失败: {e}")
                overall_result["tests_run"] += 1
                overall_result["tests_failed"] += 1
        
        # 获取性能统计
        if hasattr(self.ai_model, 'get_performance_stats'):
            overall_result["performance_stats"] = self.ai_model.get_performance_stats()
        
        overall_result["end_time"] = time.time()
        overall_result["total_time"] = overall_result["end_time"] - overall_result["start_time"]
        
        # 清理资源
        self.ai_model.cleanup_resources()
        
        return overall_result
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """生成测试报告"""
        report = f"""
# AI模型优化测试报告

## 测试概览
- 测试开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(results['start_time']))}
- 测试总耗时: {results['total_time']:.2f}秒
- 测试总数: {results['tests_run']}
- 通过测试: {results['tests_passed']}
- 失败测试: {results['tests_failed']}
- 成功率: {(results['tests_passed'] / results['tests_run'] * 100):.1f}%

## 详细测试结果
"""
        
        for test_result in self.test_results:
            report += f"""
### {test_result['test_name']}
- 状态: {'✅ 通过' if test_result.get('success', False) else '❌ 失败'}
- 耗时: {test_result.get('end_time', 0) - test_result.get('start_time', 0):.2f}秒
"""
            if test_result.get('error'):
                report += f"- 错误: {test_result['error']}\n"
            
            # 添加特定测试的详细信息
            if test_result['test_name'] == 'performance_under_load':
                report += f"- 并发请求数: {test_result.get('concurrent_requests', 0)}\n"
                report += f"- 成功请求: {test_result.get('successful_requests', 0)}\n"
                report += f"- 失败请求: {test_result.get('failed_requests', 0)}\n"
                report += f"- 平均响应时间: {test_result.get('average_response_time', 0):.2f}秒\n"
        
        # 添加性能统计
        if results.get('performance_stats'):
            stats = results['performance_stats']
            report += f"""
## 性能统计
- 总请求数: {stats.get('total_requests', 0)}
- 成功请求数: {stats.get('successful_requests', 0)}
- 失败请求数: {stats.get('failed_requests', 0)}
- 平均响应时间: {stats.get('average_response_time', 0):.2f}秒
"""
        
        return report

def main():
    """主函数"""
    try:
        tester = AIModelTester()
        results = tester.run_all_tests()
        
        # 生成报告
        report = tester.generate_report(results)
        
        # 保存报告
        with open('ai_model_test_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 输出结果
        print(report)
        print(f"\n测试完成！详细报告已保存到 ai_model_test_report.md")
        
        return 0 if results['tests_failed'] == 0 else 1
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
