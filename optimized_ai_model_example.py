#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化后的AI模型示例代码
展示如何实现更好的错误处理、性能监控和资源管理
"""

import json
import logging
import time
import re
import copy
import base64
from typing import Dict, List, Any, Optional, Tuple, Callable
import threading
import queue
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum

# 导入自定义工具
from modules.utils import retry_with_exponential_backoff

# Gemini API
from google import genai
from google.genai import types
from google.api_core.exceptions import GoogleAPIError, InvalidArgument, ResourceExhausted

# OpenAI API
import requests
from requests.exceptions import RequestException

logger = logging.getLogger("OptimizedAIModel")

class ModelType(Enum):
    """模型类型枚举"""
    GEMINI = "gemini"
    OPENAI = "openai"
    UNKNOWN = "unknown"

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    peak_memory_usage: float = 0.0
    cache_hit_rate: float = 0.0

@dataclass
class RequestContext:
    """请求上下文数据类"""
    request_id: str
    start_time: float
    model_type: ModelType
    prompt_length: int
    stream: bool = False

class AIModelError(Exception):
    """AI模型错误类"""
    def __init__(self, message: str, error_code: str = None, retry_after: int = None):
        super().__init__(message)
        self.error_code = error_code
        self.retry_after = retry_after

class OptimizedAIModel:
    """
    优化后的AI模型类
    
    特性：
    - 智能重试机制
    - 性能监控
    - 资源管理
    - 缓存机制
    - 错误恢复
    """

    def __init__(self, config_manager=None):
        """初始化优化的AI模型"""
        self.config_manager = config_manager
        self._clients = {}  # 多客户端管理
        self._current_model_config = {}
        self._performance_metrics = PerformanceMetrics()
        self._request_cache = {}  # 简单缓存
        self._rate_limiter = RateLimiter()
        self._circuit_breaker = CircuitBreaker()
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        if config_manager:
            self._current_model_config = config_manager.get_ai_model()
        
        self._initialize_model()

    @contextmanager
    def _request_context(self, prompt: str, model_type: ModelType, stream: bool = False):
        """请求上下文管理器"""
        request_id = f"{int(time.time() * 1000)}_{threading.current_thread().ident}"
        context = RequestContext(
            request_id=request_id,
            start_time=time.time(),
            model_type=model_type,
            prompt_length=len(prompt),
            stream=stream
        )
        
        logger.info(f"开始请求 {request_id}: {model_type.value}, 提示词长度: {context.prompt_length}")
        
        try:
            yield context
            # 成功完成
            response_time = time.time() - context.start_time
            self._update_performance_metrics(True, response_time)
            logger.info(f"请求 {request_id} 完成，耗时: {response_time:.2f}秒")
            
        except Exception as e:
            # 请求失败
            response_time = time.time() - context.start_time
            self._update_performance_metrics(False, response_time)
            logger.error(f"请求 {request_id} 失败，耗时: {response_time:.2f}秒，错误: {e}")
            raise

    def _update_performance_metrics(self, success: bool, response_time: float):
        """更新性能指标"""
        with self._lock:
            self._performance_metrics.total_requests += 1
            if success:
                self._performance_metrics.successful_requests += 1
            else:
                self._performance_metrics.failed_requests += 1
            
            # 更新平均响应时间
            total = self._performance_metrics.total_requests
            current_avg = self._performance_metrics.average_response_time
            self._performance_metrics.average_response_time = (
                (current_avg * (total - 1) + response_time) / total
            )

    def get_performance_metrics(self) -> PerformanceMetrics:
        """获取性能指标"""
        with self._lock:
            return copy.deepcopy(self._performance_metrics)

    def _get_cache_key(self, prompt: str, model_config: dict) -> str:
        """生成缓存键"""
        import hashlib
        content = f"{prompt}_{model_config.get('model_name', '')}_{model_config.get('temperature', 0.7)}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[str]:
        """从缓存获取结果"""
        return self._request_cache.get(cache_key)

    def _set_cache(self, cache_key: str, result: str, ttl: int = 3600):
        """设置缓存"""
        # 简单的TTL实现
        expire_time = time.time() + ttl
        self._request_cache[cache_key] = (result, expire_time)
        
        # 清理过期缓存
        current_time = time.time()
        expired_keys = [k for k, (_, exp_time) in self._request_cache.items() if exp_time < current_time]
        for key in expired_keys:
            del self._request_cache[key]

    def generate_content_optimized(self, 
                                 prompt: str,
                                 stream: bool = False,
                                 callback: Callable = None,
                                 system_instruction: Optional[str] = None,
                                 use_cache: bool = True) -> Optional[str]:
        """
        优化的内容生成方法
        
        Args:
            prompt: 提示文本
            stream: 是否使用流式输出
            callback: 回调函数
            system_instruction: 系统指令
            use_cache: 是否使用缓存
            
        Returns:
            生成的内容
        """
        if not self._current_model_config:
            raise AIModelError("未配置AI模型", "NO_MODEL_CONFIG")

        model_type = ModelType(self._current_model_config.get("api_type", "").lower())
        
        # 检查缓存
        if use_cache and not stream:
            cache_key = self._get_cache_key(prompt, self._current_model_config)
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info("使用缓存结果")
                return cached_result[0]  # 返回缓存的内容

        # 速率限制检查
        self._rate_limiter.wait_if_needed()
        
        # 熔断器检查
        if not self._circuit_breaker.can_execute():
            raise AIModelError("服务暂时不可用，熔断器已开启", "CIRCUIT_BREAKER_OPEN")

        with self._request_context(prompt, model_type, stream) as context:
            try:
                if model_type == ModelType.GEMINI:
                    result = self._gemini_generate_optimized(prompt, stream, callback, system_instruction)
                elif model_type == ModelType.OPENAI:
                    result = self._openai_generate_optimized(prompt, stream, callback, system_instruction)
                else:
                    raise AIModelError(f"不支持的模型类型: {model_type.value}", "UNSUPPORTED_MODEL")
                
                # 缓存结果
                if use_cache and not stream and result:
                    cache_key = self._get_cache_key(prompt, self._current_model_config)
                    self._set_cache(cache_key, result)
                
                self._circuit_breaker.record_success()
                return result
                
            except Exception as e:
                self._circuit_breaker.record_failure()
                raise

    def cleanup_resources(self):
        """清理资源"""
        try:
            with self._lock:
                # 清理客户端
                for client_type, client in self._clients.items():
                    try:
                        if hasattr(client, 'close'):
                            client.close()
                    except:
                        pass
                
                self._clients.clear()
                self._request_cache.clear()
                
                logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")

    def __del__(self):
        """析构函数"""
        try:
            self.cleanup_resources()
        except:
            pass

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests_per_minute: int = 60):
        self.max_requests = max_requests_per_minute
        self.requests = []
        self._lock = threading.Lock()
    
    def wait_if_needed(self):
        """如果需要则等待"""
        with self._lock:
            now = time.time()
            # 清理1分钟前的请求
            self.requests = [req_time for req_time in self.requests if now - req_time < 60]
            
            if len(self.requests) >= self.max_requests:
                # 需要等待
                wait_time = 60 - (now - self.requests[0])
                if wait_time > 0:
                    logger.info(f"速率限制，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
            
            self.requests.append(now)

class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self._lock = threading.Lock()
    
    def can_execute(self) -> bool:
        """检查是否可以执行请求"""
        with self._lock:
            if self.state == "CLOSED":
                return True
            elif self.state == "OPEN":
                if time.time() - self.last_failure_time > self.recovery_timeout:
                    self.state = "HALF_OPEN"
                    return True
                return False
            else:  # HALF_OPEN
                return True
    
    def record_success(self):
        """记录成功"""
        with self._lock:
            self.failure_count = 0
            self.state = "CLOSED"
    
    def record_failure(self):
        """记录失败"""
        with self._lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
                logger.warning(f"熔断器开启，失败次数: {self.failure_count}")
