#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自定义标题栏模块
提供无边框窗口的标题栏功能，包括标题、图标和窗口控制按钮
"""

from PySide6.QtWidgets import (
    QHBoxLayout, QLabel, QPushButton, QFrame, QApplication,
    QMenu
)
from PySide6.QtCore import Qt, QPoint, Signal
from PySide6.QtGui import QIcon, QFont, QMouseEvent, QAction

class TitleBar(QFrame):
    """
    自定义标题栏类
    提供标题、图标和窗口控制按钮（最小化、最大化/还原、关闭）
    支持拖动窗口
    """

    # 定义信号
    minimizeClicked = Signal()
    maximizeClicked = Signal()
    closeClicked = Signal()

    # 功能按钮信号
    openProjectClicked = Signal()
    saveProjectClicked = Signal()
    settingsClicked = Signal(str)  # 参数为设置类型
    stopTaskClicked = Signal()     # 停止任务信号
    themeChanged = Signal(str)     # 参数为主题名称
    aboutClicked = Signal()

    def __init__(self, parent=None):
        """
        初始化标题栏

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 设置固定高度
        self.setFixedHeight(30)

        # 设置鼠标跟踪
        self.setMouseTracking(True)

        # 初始化变量
        self._is_pressed = False
        self._start_pos = QPoint()
        self._is_maximized = False
        self._is_dark_theme = True  # 默认使用暗色主题

        # 设置样式
        self.setObjectName("titleBar")
        self.setStyleSheet("QFrame#titleBar { background-color: transparent; }")

        # 创建UI
        self._setup_ui()

    def _setup_ui(self):
        """设置UI"""
        # 创建水平布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(0)

        # 创建图标标签
        self.icon_label = QLabel(self)
        self.icon_label.setFixedSize(20, 20)
        layout.addWidget(self.icon_label)
        layout.addSpacing(5)

        # 创建标题标签
        self.title_label = QLabel(self)
        self.title_label.setObjectName("titleLabel")
        font = QFont("Microsoft YaHei UI", 10)
        self.title_label.setFont(font)
        layout.addWidget(self.title_label)

        # 添加伸缩项，使控制按钮靠右
        layout.addStretch()

        # 设置按钮样式 - 将在 _update_button_style 方法中设置

        # 创建功能按钮
        self.function_buttons = []

        # 设置按钮样式 - 使用暗色主题默认颜色
        button_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                color: white;
                font-size: 12px;
                padding: 2px 8px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """

        # 创建打开项目按钮
        self.open_button = QPushButton("打开项目", self)
        self.open_button.setStyleSheet(button_style)
        self.open_button.clicked.connect(self.openProjectClicked.emit)
        self.function_buttons.append(self.open_button)
        layout.addWidget(self.open_button)

        # 创建保存项目按钮
        self.save_button = QPushButton("保存项目", self)
        self.save_button.setStyleSheet(button_style)
        self.save_button.clicked.connect(self.saveProjectClicked.emit)
        self.function_buttons.append(self.save_button)
        layout.addWidget(self.save_button)

        # 创建设置按钮
        self.settings_button = QPushButton("设置", self)
        self.settings_button.setStyleSheet(button_style)
        self.settings_button.clicked.connect(self._show_settings_menu)
        self.function_buttons.append(self.settings_button)
        layout.addWidget(self.settings_button)

        # 创建停止按钮
        self.stop_button = QPushButton("停止", self)
        self.stop_button.setStyleSheet(button_style + """
            QPushButton {
                color: #ff6b6b;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 107, 107, 0.2);
                color: #ff4757;
            }
            QPushButton:pressed {
                background-color: rgba(255, 107, 107, 0.3);
            }
            QPushButton:disabled {
                color: #666666;
            }
        """)
        self.stop_button.clicked.connect(self.stopTaskClicked.emit)
        self.stop_button.setEnabled(False)  # 默认禁用
        self.stop_button.setToolTip("停止当前任务")
        self.function_buttons.append(self.stop_button)
        layout.addWidget(self.stop_button)

        # 创建主题切换按钮
        self.theme_button = QPushButton("主题", self)
        self.theme_button.setStyleSheet(button_style)
        self.theme_button.clicked.connect(self._show_theme_menu)
        self.function_buttons.append(self.theme_button)
        layout.addWidget(self.theme_button)

        # 创建关于按钮
        self.about_button = QPushButton("关于", self)
        self.about_button.setStyleSheet(button_style)
        self.about_button.clicked.connect(self.aboutClicked.emit)
        self.function_buttons.append(self.about_button)
        layout.addWidget(self.about_button)

        # 添加间隔
        layout.addSpacing(10)

        # 创建最小化按钮
        self.min_button = QPushButton(self)
        self.min_button.setObjectName("minButton")
        self.min_button.setFixedSize(30, 30)
        self.min_button.setIcon(QIcon("resources/icons/minimize.png"))
        self.min_button.setToolTip("最小化")
        self.min_button.clicked.connect(self.minimizeClicked.emit)
        layout.addWidget(self.min_button)

        # 创建最大化/还原按钮
        self.max_button = QPushButton(self)
        self.max_button.setObjectName("maxButton")
        self.max_button.setFixedSize(30, 30)
        self.max_button.setIcon(QIcon("resources/icons/maximize.png"))
        self.max_button.setToolTip("最大化")
        self.max_button.clicked.connect(self._on_max_button_clicked)
        layout.addWidget(self.max_button)

        # 创建关闭按钮
        self.close_button = QPushButton(self)
        self.close_button.setObjectName("closeButton")
        self.close_button.setFixedSize(30, 30)
        self.close_button.setIcon(QIcon("resources/icons/close.png"))
        self.close_button.setToolTip("关闭")
        self.close_button.clicked.connect(self.closeClicked.emit)
        layout.addWidget(self.close_button)

        # 更新按钮样式
        self._update_button_style()

        # 设置布局
        self.setLayout(layout)

    def _on_max_button_clicked(self):
        """处理最大化/还原按钮点击"""
        self._is_maximized = not self._is_maximized

        # 根据主题和窗口状态设置图标
        if self._is_dark_theme:
            if self._is_maximized:
                self.max_button.setIcon(QIcon("resources/icons/restore.png"))
            else:
                self.max_button.setIcon(QIcon("resources/icons/maximize.png"))
        else:
            if self._is_maximized:
                self.max_button.setIcon(QIcon("resources/icons/restore_black.png"))
            else:
                self.max_button.setIcon(QIcon("resources/icons/maximize_black.png"))

        # 更新工具提示
        if self._is_maximized:
            self.max_button.setToolTip("还原")
        else:
            self.max_button.setToolTip("最大化")

        self.maximizeClicked.emit()

    def set_title(self, title):
        """
        设置标题

        Args:
            title: 标题文本
        """
        self.title_label.setText(title)

    def set_icon(self, icon):
        """
        设置图标

        Args:
            icon: QIcon实例
        """
        self.icon_label.setPixmap(icon.pixmap(20, 20))

    def mousePressEvent(self, event: QMouseEvent):
        """
        鼠标按下事件

        Args:
            event: 鼠标事件
        """
        if event.button() == Qt.LeftButton:
            self._is_pressed = True
            self._start_pos = event.globalPosition().toPoint() - self.window().pos()

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """
        鼠标移动事件

        Args:
            event: 鼠标事件
        """
        if self._is_pressed:
            if self._is_maximized:
                # 如果窗口已最大化，则先还原窗口
                self._on_max_button_clicked()

                # 调整鼠标位置，使其相对于窗口的位置保持不变
                ratio = event.globalPosition().toPoint().x() / QApplication.primaryScreen().size().width()
                new_width = self.window().width()
                new_x = int(ratio * new_width)
                self._start_pos = QPoint(new_x, self._start_pos.y())

            # 移动窗口
            self.window().move(event.globalPosition().toPoint() - self._start_pos)

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """
        鼠标释放事件

        Args:
            event: 鼠标事件
        """
        self._is_pressed = False
        super().mouseReleaseEvent(event)

    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """
        鼠标双击事件

        Args:
            event: 鼠标事件
        """
        if event.button() == Qt.LeftButton:
            # 双击标题栏最大化/还原窗口
            self._on_max_button_clicked()

        super().mouseDoubleClickEvent(event)

    def _show_settings_menu(self):
        """显示设置菜单"""
        menu = QMenu(self)

        # 添加菜单项
        model_action = QAction("AI模型设置", self)
        model_action.triggered.connect(lambda: self.settingsClicked.emit("model"))
        menu.addAction(model_action)

        general_action = QAction("常用设置", self)
        general_action.triggered.connect(lambda: self.settingsClicked.emit("general"))
        menu.addAction(general_action)

        prompt_action = QAction("提示词设置", self)
        prompt_action.triggered.connect(lambda: self.settingsClicked.emit("prompt"))
        menu.addAction(prompt_action)

        # 显示菜单
        menu.exec(self.settings_button.mapToGlobal(QPoint(0, self.settings_button.height())))

    def _show_theme_menu(self):
        """显示主题菜单"""
        menu = QMenu(self)

        # 添加菜单项
        light_action = QAction("浅色主题", self)
        light_action.triggered.connect(lambda: self.themeChanged.emit("light"))
        menu.addAction(light_action)

        dark_action = QAction("暗色主题", self)
        dark_action.triggered.connect(lambda: self.themeChanged.emit("dark"))
        menu.addAction(dark_action)

        # 显示菜单
        menu.exec(self.theme_button.mapToGlobal(QPoint(0, self.theme_button.height())))

    def set_stop_button_enabled(self, enabled):
        """
        设置停止按钮的启用状态

        Args:
            enabled: 是否启用停止按钮
        """
        self.stop_button.setEnabled(enabled)
        if enabled:
            self.stop_button.setToolTip("停止当前任务")
        else:
            self.stop_button.setToolTip("当前无正在运行的任务")

    def set_theme(self, is_dark_theme):
        """
        设置主题

        Args:
            is_dark_theme: 是否是暗色主题
        """
        if self._is_dark_theme != is_dark_theme:
            self._is_dark_theme = is_dark_theme

            # 更新功能按钮样式
            if is_dark_theme:
                # 暗色主题 - 使用默认的暗色背景
                button_style = """
                    QPushButton {
                        background-color: transparent;
                        border: none;
                        color: white;
                        font-size: 12px;
                        padding: 2px 8px;
                    }
                    QPushButton:hover {
                        background-color: rgba(255, 255, 255, 0.1);
                    }
                    QPushButton:pressed {
                        background-color: rgba(255, 255, 255, 0.2);
                    }
                """
            else:
                # 浅色主题
                button_style = """
                    QPushButton {
                        background-color: transparent;
                        border: none;
                        color: black;
                        font-size: 12px;
                        padding: 2px 8px;
                    }
                    QPushButton:hover {
                        background-color: rgba(0, 0, 0, 0.1);
                    }
                    QPushButton:pressed {
                        background-color: rgba(0, 0, 0, 0.2);
                    }
                """

            # 应用样式到功能按钮
            self.open_button.setStyleSheet(button_style)
            self.save_button.setStyleSheet(button_style)
            self.settings_button.setStyleSheet(button_style)

            # 停止按钮特殊样式
            stop_button_style = button_style + """
                QPushButton {
                    color: #ff6b6b;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(255, 107, 107, 0.2);
                    color: #ff4757;
                }
                QPushButton:pressed {
                    background-color: rgba(255, 107, 107, 0.3);
                }
                QPushButton:disabled {
                    color: #666666;
                }
            """
            self.stop_button.setStyleSheet(stop_button_style)

            self.theme_button.setStyleSheet(button_style)
            self.about_button.setStyleSheet(button_style)

            # 更新按钮样式
            self._update_button_style()

    def add_function_button(self, icon_path, tooltip, signal_func=None):
        """
        添加功能按钮

        Args:
            icon_path: 图标路径
            tooltip: 提示文本
            signal_func: 信号连接函数

        Returns:
            创建的按钮
        """
        button = QPushButton(self)
        button.setFixedSize(30, 30)
        button.setIcon(QIcon(icon_path))
        button.setToolTip(tooltip)
        if signal_func:
            button.clicked.connect(signal_func)
        self.function_buttons.append(button)
        return button

    def _update_button_style(self):
        """更新按钮样式"""
        if self._is_dark_theme:
            # 暗色主题按钮样式
            button_style = """
                QPushButton {
                    background-color: transparent;
                    border: none;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }
                QPushButton:pressed {
                    background-color: rgba(255, 255, 255, 0.2);
                }
            """

            # 关闭按钮特殊样式
            close_button_style = """
                QPushButton {
                    background-color: transparent;
                    border: none;
                }
                QPushButton:hover {
                    background-color: rgba(232, 17, 35, 0.9);
                }
                QPushButton:pressed {
                    background-color: rgba(232, 17, 35, 0.7);
                }
            """

            # 设置白色图标
            self.min_button.setIcon(QIcon("resources/icons/minimize.png"))
            self.close_button.setIcon(QIcon("resources/icons/close.png"))

            # 根据窗口状态设置最大化/还原图标
            if self._is_maximized:
                self.max_button.setIcon(QIcon("resources/icons/restore.png"))
            else:
                self.max_button.setIcon(QIcon("resources/icons/maximize.png"))
        else:
            # 浅色主题按钮样式
            button_style = """
                QPushButton {
                    background-color: transparent;
                    border: none;
                }
                QPushButton:hover {
                    background-color: rgba(0, 0, 0, 0.1);
                }
                QPushButton:pressed {
                    background-color: rgba(0, 0, 0, 0.2);
                }
            """

            # 关闭按钮特殊样式（浅色主题下也使用红色背景）
            close_button_style = """
                QPushButton {
                    background-color: transparent;
                    border: none;
                }
                QPushButton:hover {
                    background-color: rgba(232, 17, 35, 0.9);
                }
                QPushButton:pressed {
                    background-color: rgba(232, 17, 35, 0.7);
                }
            """

            # 设置黑色图标
            self.min_button.setIcon(QIcon("resources/icons/minimize_black.png"))
            self.close_button.setIcon(QIcon("resources/icons/close_black.png"))

            # 根据窗口状态设置最大化/还原图标
            if self._is_maximized:
                self.max_button.setIcon(QIcon("resources/icons/restore_black.png"))
            else:
                self.max_button.setIcon(QIcon("resources/icons/maximize_black.png"))

        # 应用样式
        self.min_button.setStyleSheet(button_style)
        self.max_button.setStyleSheet(button_style)
        self.close_button.setStyleSheet(close_button_style)
